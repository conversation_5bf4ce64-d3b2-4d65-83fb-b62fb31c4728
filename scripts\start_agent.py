#!/usr/bin/env python3
"""
Start v-switch agent.
"""

import sys
import signal
import argparse
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from v_switch.config.agent_config import AgentConfig
from v_switch.agent.agent import VSwitchAgent
from v_switch.utils.logging_utils import setup_logging


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    print(f"\nReceived signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Start v-switch agent")
    parser.add_argument(
        "--config", 
        default="config/agent_config.yaml",
        help="Path to agent configuration file"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in dry-run mode (log commands but don't execute)"
    )
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        print(f"Loading configuration from {args.config}")
        config = AgentConfig.from_file(args.config)
        
        # Setup logging
        setup_logging(config.logging, f"v-switch-agent-{config.agent.agent_id}")
        
        # Create agent
        print(f"Initializing agent {config.agent.agent_id}...")
        agent = VSwitchAgent(config, dry_run=args.dry_run)
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Start agent
        print("Starting agent...")
        if not agent.start():
            print("Failed to start agent")
            return 1
        
        print(f"V-Switch agent {config.agent.agent_id} started successfully")
        if args.dry_run:
            print("Running in DRY-RUN mode - commands will be logged but not executed")
        print("Press Ctrl+C to stop")
        
        # Keep running
        try:
            while True:
                signal.pause()
        except KeyboardInterrupt:
            pass
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        # Cleanup
        print("Stopping agent...")
        try:
            if 'agent' in locals():
                agent.stop()
        except Exception as e:
            print(f"Error during cleanup: {e}")
        
        print("Agent stopped")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

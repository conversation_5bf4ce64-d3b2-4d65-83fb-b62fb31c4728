"""
API server for v-switch using FastAPI.
"""

import logging
import threading
import asyncio
from typing import Dict, Any, Optional
import uvicorn
from fastapi import FastAPI, HTTPException, Query, Path as PathParam
from pydantic import BaseModel

from v_switch.common.models import ServerMetadata
from ..config.server_config import ServerConfig
from ..core_service.core_service import CoreService

class TestRequest(BaseModel):
    """Request model for test endpoint."""
    tenant_id: str
    test_id: int

class TestDeleteRequest(BaseModel):
    """Request model for deleting test resources."""
    tenant_id: str
    test_id: int

# Pydantic models for request/response data
class CreateSubnetRequest(BaseModel):
    """Request model for creating subnet."""
    tenant_id: str
    vlan_id: int
    subnet_gw_ip: str

class QuerySubnetRequest(BaseModel):
    """Request model for querying subnet."""
    tenant_id: str
    vlan_id: int

class DeleteSubnetRequest(BaseModel):
    """Request model for deleting subnet."""
    tenant_id: str
    vlan_id: int

class MountEIPRequest(BaseModel):
    """Request model for mounting EIP."""
    tenant_id: str
    vlan_id: int
    eip: str
    internal_ip: str
    eip_gateway: Optional[str] = None

class UnmountEIPRequest(BaseModel):
    """Request model for unmounting EIP."""
    tenant_id: str
    vlan_id: int
    eip: str
    internal_ip: str


class SuccessResponse(BaseModel):
    """Success response model."""
    success: bool = True
    instruction_id: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: bool = True
    message: str
    status_code: int


class HealthResponse(BaseModel):
    """Health check response model."""
    success: bool = True
    status: str = "healthy"
    running: bool


class StatusResponse(BaseModel):
    """Server status response model."""
    success: bool = True
    running: bool
    metadata: Optional[ServerMetadata] = None


class InstructionResponse(BaseModel):
    """Instruction status response model."""
    success: bool = True
    instruction: Optional[Dict[str, Any]] = None


def create_fastapi_app(core_service: CoreService) -> FastAPI:
    """Create FastAPI application with all routes.

    Args:
        core_service: Core service instance

    Returns:
        FastAPI application instance
    """
    app = FastAPI(
        title="V-Switch API",
        description="API server for v-switch network management",
        version="1.0.0"
    )

    logger = logging.getLogger(__name__)

    # 健康检查
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查"""
        try:
            return HealthResponse(
                success=True,
                status="healthy",
                running=core_service.is_running()
            )
        except Exception as e:
            logger.error(f"Error in health check: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 服务状态
    @app.get("/status", response_model=StatusResponse)
    async def server_status():
        """获取服务器状态和元数据。"""
        try:
            metadata = core_service.get_server_metadata()
            return StatusResponse(
                success=True,
                running=core_service.is_running(),
                metadata=metadata
            )
        except Exception as e:
            logger.error(f"Error getting server status: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 创建子网
    @app.post("/network/subnet", response_model=SuccessResponse)
    async def create_subnet(request: CreateSubnetRequest):
        """创建子网"""
        try:
            logger.info(f"Creating subnet for tenant {request.tenant_id}, VLAN {request.vlan_id}")

            subnet_id = core_service.create_subnet(
                request.tenant_id,
                request.vlan_id,
                request.subnet_gw_ip
            )

            if subnet_id:
                return SuccessResponse(
                    success=True,
                    instruction_id=subnet_id,
                    message="Subnet created successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to create subnet")

        except ValueError as e:
            logger.error(f"Validation error creating subnet: {e}")
            raise HTTPException(status_code=400, detail=str(e))
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating subnet: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 查询子网
    @app.get("/network/subnet", response_model=SuccessResponse)
    async def query_subnet(tenant_id:str, vlan_id:int):
        """查询子网"""
        try:
            logger.info(f"Querying subnet for tenant {tenant_id}, VLAN vlan-{vlan_id}")

            subnet_config = core_service.query_subnet(
                tenant_id,
                vlan_id
            )

            if subnet_config:
                return SuccessResponse(
                    success=True,
                    data=subnet_config,
                    message="Subnet found"
                )
            else:
                raise HTTPException(status_code=404, detail="Subnet not found")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error querying subnet: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 删除子网
    @app.delete("/network/subnet", response_model=SuccessResponse)
    async def delete_subnet(request: DeleteSubnetRequest):
        """删除子网"""
        try:
            logger.info(f"Deleting subnet for tenant {request.tenant_id}, VLAN {request.vlan_id}")

            success = core_service.delete_subnet(
                request.tenant_id,
                request.vlan_id
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="Subnet deleted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to delete subnet")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error deleting subnet: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")


    # 挂载EIP
    @app.post("/network/eip/mount", response_model=SuccessResponse)
    async def mount_eip(request: MountEIPRequest):
        """挂载EIP"""
        try:
            logger.info(f"Mounting EIP {request.eip} to {request.internal_ip} for tenant {request.tenant_id}, VLAN {request.vlan_id}")

            success = core_service.mount_eip(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.internal_ip,
                request.eip_gateway
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP mounted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to mount EIP")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error mounting EIP: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")

    # 卸载EIP
    @app.post("/network/eip/unmount", response_model=SuccessResponse)
    async def unmount_eip(request: UnmountEIPRequest):
        """卸载EIP"""
        try:
            logger.info(f"Unmounting EIP {request.eip} from {request.internal_ip} for tenant {request.tenant_id}, VLAN {request.vlan_id}")

            success = core_service.unmount_eip(
                request.tenant_id,
                request.vlan_id,
                request.eip,
                request.internal_ip
            )

            if success:
                return SuccessResponse(
                    success=True,
                    message="EIP unmounted successfully"
                )
            else:
                raise HTTPException(status_code=500, detail="Failed to unmount EIP")

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error unmounting EIP: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {e}")
    
    return app


class APIServer:
    """Main API server class using FastAPI."""

    def __init__(self, config: ServerConfig, core_service: CoreService):
        """Initialize API server.

        Args:
            config: Server configuration
            core_service: Core service instance
        """
        self.config = config
        self.core_service = core_service
        self.logger = logging.getLogger(__name__)

        # Create FastAPI app
        self.app = create_fastapi_app(core_service)

        # Server state
        self.server = None
        self.server_thread = None
        self._running = False
    
    def start(self) -> bool:
        """Start the API server.

        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("API server is already running")
                return True

            # Configure uvicorn
            config = uvicorn.Config(
                app=self.app,
                host="0.0.0.0",
                port=self.config.server.port,
                log_level="info",
                access_log=True
            )

            self.server = uvicorn.Server(config)

            # Start server in background thread
            self.server_thread = threading.Thread(
                target=self._run_server,
                name="APIServer",
                daemon=True
            )

            self._running = True
            self.server_thread.start()

            self.logger.info(f"FastAPI server started on port {self.config.server.port}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start API server: {e}")
            return False
    
    def stop(self) -> None:
        """Stop the API server."""
        try:
            if not self._running:
                return

            self.logger.info("Stopping API server...")
            self._running = False

            # Shutdown uvicorn server
            if self.server:
                self.server.should_exit = True

            # Wait for server thread to finish
            if self.server_thread and self.server_thread.is_alive():
                self.server_thread.join(timeout=5.0)

            self.logger.info("API server stopped")

        except Exception as e:
            self.logger.error(f"Error stopping API server: {e}")
    
    def _run_server(self) -> None:
        """Run the uvicorn server."""
        try:
            self.logger.info("Starting FastAPI server loop")
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.server.serve())
        except Exception as e:
            if self._running:  # Only log if we're supposed to be running
                self.logger.error(f"FastAPI server error: {e}")
    
    def is_running(self) -> bool:
        """Check if API server is running.
        
        Returns:
            True if running
        """
        return self._running

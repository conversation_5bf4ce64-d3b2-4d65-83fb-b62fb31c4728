## api server 模块设计

提供外部访问的api接口, 接收外部请求, 调用 core service 模块的业务逻辑处理;

### 业务功能
#### 创建子网
  参数：tenant_id, vlan_id, subnet_gw_ip
  调用 core service 模块的创建子网逻辑;

#### 查询子网
  参数：tenant_id, vlan_id
  调用 core service 模块的查询子网逻辑;

#### 挂载EIP
  参数：tenant_id, vlan_id, eip, internal_ip
  调用 core service 模块的挂载EIP逻辑;

## core service 模块设计
core service 模块是整个系统的业务逻辑核心, 负责处理所有的业务逻辑

### 业务功能
#### 初始化 etcd 连接
  启动服务时加载配置文件, 根据etcd配置连接到 etcd
server_config 配置文件
```yaml
server:
  port: 30090
  shard_count: 32
etcd:
  host: localhost
  port: 2379
  timeout: 5.0
  username: ''
  password: ''
eip:
  gateway: *************
monnet:
  ip_min: ***********
  ip_max: *************
  host: *************
  https: true
  port: 30428
  ext_ports: [
    69,
    9879
  ]
logging:
  level: INFO
  file: ''
```

#### 初始化元数据
连接etcd成功后, 检查 /network/server/metadata 是否存在, 如果不存在则创建;

元数据样例:
```json
{
  "server":{
      "shard_count": 32
  },
  "agent":[
    {
      "online": true,
      "agent_id":"agent-1",
      "offline_time": 0,
      "shard":["1"]
    },
    {
      "online": true,
      "agent_id":"agent-2",
      "offline_time": 0,
      "shard":["2"]
    }
  ]
}
```

#### agent注册处理
  启动时查询元数据 /network/server/metadata;
  从元数据中获取分配的 agent 列表;
  根据 agent 列表, 依次查询 etcd: /network/agent/register/{agent_id} 注册信息进行检查;

  检查逻辑:
  1. 如果 agent 注册信息不存在或 status 为 error, 则执行逻辑同 DELETE 事件;
  2. 如果 agent注册信息 status 为 running, 则再检查元数据 agent 的 online 状态;
  3. 如果 online 状态为 false, 则修改 /network/server/metadata 数据, 将对应 agent 的 online 字段修改为 true, offline_time 修改为 0;
  4. 检查是否有未分配的 shard, 如果有则分配给当前 agent;


  监听ctcd 的 /network/agent/register/ 目录, 接收到事件时执行处理逻辑;

  DELETE 事件: 
  1. 修改 /network/server/metadata 数据, 将对应 agent 的 online 字段修改为 false, offline_time 修改为当前时间, shard 字段设置为空; 
  2. 如果有其他 online 状态的 agent, 则重新分配分片给其他 online 状态的 agent;

  PUT 事件: 
  1. 根据 prev_kv 判断 status 字段变化;
  2. 如果是 running -> error, 执行逻辑同 DELETE 事件;
  3. 如果是 error -> running 或 null -> running, 则修改 /network/server/metadata 数据, 将对应 agent 的 online 字段修改为 true, offline_time 修改为 0;
  4. 检查是否有未分配的 shard, 如果有则分配给当前 agent;

/network/agent/register/{agent_id} 样例:
```json
{
  "status":"running | error",
  "agent_id": "agent-1",
  "lase_heartbeat_time": 1751526218187
}
```

#### 创建子网
  参数: tenant_id, vlan_id, subnet_gw_ip
  
  1. 检查是否有可用 agent, 如果没有则返回错误;
  2. 检查 vlan_id 是否在允许的范围内, 如果不在范围内, 则返回错误;
  3. 检查etcd中key: /network/server/subnet/{shard_id}/vlan-{vlan_id} 是否存在, 如果存在则返回错误;
  4. 创建子网配置数据;
  5. 配置参数中添加 subnet 部分;
  6. 配置参数中添加 monnet 部分, mon_ip 从 mon_ip_allocator 分配;
  7. 修改配置参数 version + 1
  8. 设置配置参数 status 为 updating
  9.  将子网配置数据写入 etcd, key: /network/server/subnet/{shard_id}/vlan-{vlan_id};

#### 查询子网
  参数: tenant_id, vlan_id

  1. 从 etcd 查询子网配置数据 key: /network/server/subnet/{shard_id}/vlan-{vlan_id};
  2. 如果配置参数不存在则返回错误;
  3. 返回子网配置数据

#### 挂载EIP
  参数: tenant_id, vlan_id, eip, internal_ip, eip_gateway(可选, 默认为 conf.eip.gateway的值)

1. 检查是否有可用 agent, 如果没有则返回错误;
2. 从 etcd 查询子网配置数据 key: /network/server/subnet/{shard_id}/vlan-{vlan_id};
3. 如果配置参数不存在则返回错误, 存在则添加 eip_mount 部分;
4. 修改配置参数 version + 1
5. 设置配置参数 status 为 updating
6. 将更新后的配置数据写入 etcd, key: /network/server/subnet/{shard_id}/vlan-{vlan_id};

##### 子网配置数据样例
```json
{
  "id":"",
  "version":0,
  "tenant_id":"",
  "shard_id":"",
  "vlan_id":"",
  "status":"activate | error | updating | deletion",
  "subnet":{
    "gateway_ip":"***********",
    "pre_ip":"10.50.101"
  },
  "monnet":{
    "mon_ip":"***********",
    "mon_m_ip":"*************",
    "mon_m_ports": 30428,
    "ext_m_ports": [69, 9879]
  },
  "eipnet": {
    "gateway_ip": "*************",
    "mounts": [
      {
        "eip":"**************",
        "internal_ip":"***********"
      }
    ]
  }
}
```

#### 删除子网
  参数: tenant_id, vlan_id, subnet_gw_ip

1. 检查是否有可用 agent, 如果没有则返回错误;
2. 从 etcd 查询子网配置数据 key: /network/server/subnet/{shard_id}/vlan-{vlan_id};
3. 如果配置参数不存在则返回错误;
4. 检查 eipnet 部分是否为空, 如果不为空则返回错误;
5. 设置 subnet, monnet 为 null;
6. 修改配置参数 version + 1
7. 设置配置参数 status 为 deletion
8. 将更新后的配置数据写入 etcd, key: /network/server/subnet/{shard_id}/vlan-{vlan_id};

#### 卸载EIP
  参数: tenant_id, vlan_id, eip, internal_ip

1. 检查是否有可用 agent, 如果没有则返回错误;
2. 从 etcd 查询子网配置数据 key: /network/server/subnet/{shard_id}/vlan-{vlan_id};
3. 如果配置参数不存在则返回错误;
4. 检查 eipnet 部分是否为空, 为空则直接返回成功;
5. 轮询比对 eipnet.mounts 部分, 找到对应的 eip, 删除对象;
6. 修改配置参数 version + 1
7. 设置配置参数 status 为 updating
8. 将更新后的配置数据写入 etcd, key: /network/server/subnet/{shard_id}/vlan-{vlan_id};

#### agent任务监听
  core service 模块启动时, 监听 etcd: /network/agent/tasks/ 目录, 接收到事件时, 执行处理逻辑;
  PUT 事件: 
  1. 查看任务 status, 如果是 running 则忽略,  error则更新对应子网配置数据 status 为 error, complete 则继续判断任务 type, type 为 update 则更新对应子网配置数据 status 为 activate, type 为 delete 则删除对应子网配置数据;


## agent 模块设计
agent 模块是系统的执行单元, 负责执行指令, 处理网络配置等;

### 业务功能
#### 加载配置
  启动agent时加载配置文件, 根据etcd配置连接到 etcd

##### agent_config 配置文件
```yaml
agent:
  agent_id: agent-1
  check_env: true
  heartbeat_interval: 30.0
  max_retries: 3
etcd:
  host: localhost
  port: 2379
  timeout: 5.0
  username: ''
  password: ''
logging:
  level: INFO
  file: ''
```


<!-- #### 清理环境
启动服务时, 检查主机最近是否重启过(重启后, 一些配置会失效);
如果重启过, 则根据./data/subnet/{shard_id}目录下的配置数据, 生成删除命令并执行, 清理残余配置;
如果没有重启过, 则忽略; -->

#### 元数据监听
启动时, 监听 etcd: /network/server/metadata, 实时获取当前 agent 分配的 shard 列表, 当 shard 列表发生变化时, 同时变更监听的分片目录;
每新增一个分片, 创建一个监听线程, 开始监听时从 etcd: /network/server/subnet/{shard_id} 目录获取子网配置数据列表, 执行 配置命令处理 逻辑;
每删除一个分片, 关闭对应的监听线程, 关闭前先执行 配置命令处理 逻辑(只传本地配置数据进行删除), 注意: 手动关闭服务时, 不需要删除;


#### agent注册
  启动时, 初始化 heartbeat 线程;
  根据 heartbeat_interval 配置定时执行逻辑:
  1. 执行环境检查, 如果检查不通过, 则设置 status 为 error, 检查通, 过则设置为 running;
  2. 从 etcd key: /network/agent/register/{agent_id} 中获取注册数据, 没有则新建, key 绑定租约, 租约时间设置为 heartbeat_interval 的 3 倍;
  3. 修改注册数据提交到etcd, lase_heartbeat_time 设置为当前时间;
  4. 根据 key 获取租约并进行续租;

##### 环境检查
根据 check_env 配置判断, 如果为 true, 则执行环境检查, 如果检查不通过或check_env配置为false, 返回false, 检查通过则返回true;
1. 检查 ovs,nftables 是否安装;
2. 检查ip转发配置:`net.ipv4.ip_forward = 1`,`net.ipv4.conf.all.rp_filter = 0`,`net.ipv4.conf.default.rp_filter = 0`


#### 子网配置数据检查
1. 启动时根据分配的分片列表, 查询 etcd: /network/server/subnet/{shard_id} 目录下的子网配置数据列表;
2. 比对本地子网配置数据文件 ./data/subnet/{shard}/vlan-{vlan_id}.json;
3. 如果本地子网配置数据文件不存在或 version 不一致, 则执行 配置命令处理 逻辑;
4. 执行 配置命令处理 逻辑后, 同步远程子网配置数据到本地, 本地子网配置数据文件 ./data/subnet/{shard}/vlan-{vlan_id}.json;


#### 子网配置数据监听
  当监听的/network/server/subnet/{shard_id} 目录发生变化, 接收到事件时, 执行处理逻辑:
  接收到 PUT 事件: 
  1. 检查子网配置数据 status, 如果是 updating 或 deletion 则继续, 否则忽略;
  2. 创建任务数据, task_id 为 uuid, 任务 status 为 running, 任务 type 对应子网配置数据 status(updating对应update, deletion对应delete), version 对应子网配置数据 version;
  3. 任务数据写入 etcd key: /network/agent/task/{task_id};
  4. 执行 配置命令处理 逻辑;
  5. 根据配置命令执行结果, 更新任务数据到 etcd, status 为执行结果(complete | error), message 记录异常信息;
  6. 同步远程子网配置数据到本地 ./data/subnet/{shard}/vlan-{vlan_id}.json;

##### 任务数据样例
```json
{
  "task_id":"",
  "shard_id":"",
  "vlan_id":"",
  "type":"update | delete",
  "version": 0,
  "status": "running | complete | error",
  "cmds":[
    "echo 1",
    "echo 2",
    "echo 3"
  ],
  "message":""
}
```

#### 配置命令处理
入参: 任务数据(可选, 为空则不更新任务数据), 远程子网配置数据(可选), 本地子网配置数据(可选)

远程子网配置数据 和 本地子网配置数据 至少有一个存在, 如果远程子网配置数据不存在, 则根据本地子网配置数据生成全量删除命令;如果本地子网配置数据不存在, 则根据远程子网配置数据生成全量创建命令;如果都存在, 则比对差异生成更新命令;

差异比对逻辑:
  1. 比对 subnet, monnet, eipnet 几项;
  2. 远程配置为 null, 本地不为 null, 生成全量删除命令;
  3. 远程配置不为 null, 本地为 null, 生成全量创建命令;
  4. 远程和本地都不为 null, 但内容不一致, 根据本地内容生成删除命令, 根据远程内容生成创建命令;
例如: 远程配置数据为
```json
{
  "id": "subnet-1",
  "version": 1,
  "tenant_id": "tenant-1",
  "shard_id": "1",
  "vlan_id": "1003",
  "status": "activate",
  "subnet": {
    "gateway_ip": "***********",
    "pre_ip": "10.50.101"
  },
  "monnet": null,
  "eipnet": null
}
```
本地配置数据为
```json
{
  "id": "subnet-1",
  "version": 0,
  "tenant_id": "tenant-1",
  "shard_id": "1",
  "vlan_id": "1003",
  "status": "activate",
  "subnet": {
    "gateway_ip": "***********",
    "pre_ip": "10.50.101"
  },
  "monnet": {
    "mon_ip": "***********",
    "mon_m_ip": "*************",
    "mon_m_ports": 30428,
    "ext_m_ports": [69, 9879]
  },
  "eipnet": {
    "gateway_ip": "*************",
    "mounts": [
      {
        "eip":"**************",
        "internal_ip":"***********"
      }
    ]
  }
}
```
则生成添加监控网络的命令 和 删除EIP挂载的命令

生成和执行命令逻辑:
  1. 命令执行前要先进行检查, 例如: 执行 ip netns add ns-vlan${VLAN_ID} 命令前, 先执行 ip netns list | grep -q "ns-vlan${VLAN_ID}" 检查命名空间是否存在, 如果存在则跳过; 需要删除设备时, 先检查是否存在, 如果不存在则跳过;
  2. 命令执行完, 更新任务数据(如果有), 将命令添加到cmds;
  3. 如果命令执行失败, 更新任务(如果有)status为error, 异常信息更新到message, 后续命令不再执行;注意: 异常信息必须包含执行的命令, 失败原因;
  4. 所有命令执行完, 更新任务(如果有)status为complete, message 为"ok";

注意: 挂载EIP时, 先检查是否有eip设备以及默认路由, 如果没有则添加; 卸载EIP时, 则要检查是否还存在其他挂载的EIP, 如果存在其他EIP, 则不删除设备和默认路由;


##### 创建子网命令样例

  创建指令模板
  ```bash
  # 创建一个专门用于此VLAN的网络命名空间，以隔离网络环境
  ip netns add ns-vlan${VLAN_ID}

  # 创建一对虚拟以太网设备（veth pair），用于连接主机和网络命名空间
  ip link add v-lan-host-${VLAN_ID} type veth peer name v-lan-ns-${VLAN_ID}
  # 将veth pair的一端（v-lan-ns-）移动到新创建的网络命名空间中
  ip link set v-lan-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 在网络命名空间内，为veth设备配置IP地址，作为该VLAN子网的网关
  ip netns exec ns-vlan${VLAN_ID} ip addr add ${ZW_GA_WAY_IP}/24 dev v-lan-ns-${VLAN_ID}
  # 将veth pair在主机侧的一端（v-lan-host-）添加到OVS网桥br-vlan，并打上VLAN标签
  ovs-vsctl add-port br-vlan v-lan-host-${VLAN_ID}  tag=${VLAN_ID}

  # 创建用于监控的veth pair
  ip link add v-mon-host-${VLAN_ID} type veth peer name v-mon-ns-${VLAN_ID}
  # 将监控veth pair的一端移动到VLAN的网络命名空间中
  ip link set v-mon-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 在网络命名空间内为监控veth设备配置IP地址
  ip netns exec ns-vlan${VLAN_ID} ip addr add ${MON_IP}/24 dev v-mon-ns-${VLAN_ID}
  # 将监控veth pair在主机侧的一端添加到OVS网桥br-mon
  ovs-vsctl add-port br-mon v-mon-host-${VLAN_ID}

  #-------------- 统一启动网络接口
  # 启动主机侧的veth接口
  ip link set v-lan-host-${VLAN_ID} up
  # 启动网络命名空间内的veth接口
  ip netns exec ns-vlan${VLAN_ID} ip link set v-lan-ns-${VLAN_ID} up
  # 启动监控veth pair的两端接口
  ip link set v-mon-host-${VLAN_ID} up
  ip netns exec ns-vlan${VLAN_ID} ip link set v-mon-ns-${VLAN_ID} up
  # 启动网络命名空间内的本地环回接口
  ip netns exec ns-vlan${VLAN_ID} ip link set lo up

  # 在命名空间内创建nftables的nat表（如果尚不存在）
  ip netns exec ns-vlan${VLAN_ID} nft add table ip nat
  # 在命名空间内创建nftables的nat表和PREROUTING链，用于处理入站流量的目的地址转换
  ip netns exec ns-vlan${VLAN_ID} nft add chain ip nat PREROUTING { type nat hook prerouting priority -100 \; }
  ip netns exec ns-vlan${VLAN_ID} nft add chain ip nat POSTROUTING { type nat hook postrouting priority 100 \; }
  # 在命名空间内添加一条nftables规则，对发往同一子网的流量进行源地址转换（SNAT），将其源IP伪装成网关IP
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip saddr ${PRE_GA_WAY_IP}.0/24 ip daddr ${PRE_GA_WAY_IP}.0/${ZW_MASK} counter snat to ${ZW_GA_WAY_IP}
  # 添加DNAT规则，将对特定IP（***************，通常用于云元数据服务）端口的访问重定向到内部的监控管理服务, ${PORT} 的值根据conf.monnet.https 判断, 如果是 true 为 443, 否则为 80
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr *************** tcp dport ${PORT} counter dnat to ${MON_M_IP}:${MON_M_PORT}
  # 添加SNAT规则，将访问监控管理服务的出站流量的源IP伪装成监控IP
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip daddr ${MON_M_IP} tcp dport ${MON_M_PORT} counter snat to ${MON_IP}
  # 允许访问监控管理服务的其他端口(从 conf.monnet.ext_ports 获取)，同样进行SNAT
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip daddr ${MON_M_IP} tcp dport ${EXT_M_PORT} counter snat to ${MON_IP}
  ```

  ##### 挂载EIP命令样例
```bash
  # 创建另一对veth pair，用于弹性IP（EIP）的连接
  ip link add v-eip-host-${VLAN_ID} type veth peer name v-eip-ns-${VLAN_ID}
  # 将EIP veth pair的一端移动到VLAN的网络命名空间中
  ip link set v-eip-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
  # 将EIP veth pair在主机侧的一端添加到OVS网桥br-eip
  ovs-vsctl add-port br-eip v-eip-host-${VLAN_ID}

  # 启动EIP veth pair的两端接口
  ip link set v-eip-host-${VLAN_ID} up
  ip netns exec ns-vlan${VLAN_ID} ip link set v-eip-ns-${VLAN_ID} up
    
  # 在VLAN的网络命名空间内，为EIP的veth接口配置EIP地址
  ip netns exec ns-vlan${VLAN_ID} ip addr add ${EIP}/24 dev v-eip-ns-${VLAN_ID}
  # 在命名空间内添加默认路由，使得所有出站流量都通过EIP的网关出去
  ip netns exec ns-vlan${VLAN_ID} ip route add default via ${EIP_GA_WAY_IP} dev v-eip-ns-${VLAN_ID}

  # 添加DNAT规则，将所有访问该EIP的入站流量，都转发给内网的指定服务器
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr ${EIP} counter dnat to ${ZW_IP}
  # 添加SNAT规则，将内网服务器发出的、且目的地不为私有网段的流量，其源IP地址伪装成EIP地址
  ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip saddr ${ZW_IP} ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } snat to ${EIP}
```
"""
配置命令处理器 - 负责比对配置差异并生成执行命令
"""

import logging
import subprocess
from typing import Dict, Any, List, Optional, Tuple
from ..common.models import SubnetConfigParams, TaskData, TaskStatus, EipnetConfig
from .local_data import LocalDataManager


class CommandProcessor:
    """配置命令处理器"""

    def __init__(self, local_data: LocalDataManager, dry_run: bool = False):
        """初始化配置命令处理器

        Args:
            dry_run: 是否为干运行模式
        """
        self.local_data = local_data
        self.dry_run = dry_run
        self.logger = logging.getLogger(__name__)

    # 样例代码
    def process_config_changes(self, task_data: TaskData, 
                               remote_config: SubnetConfigParams, 
                               local_config: SubnetConfigParams) -> bool:
        """处理配置变更

        Args:
            task_data: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        if not remote_config and not local_config:
            raise ValueError(
                "remote_config and local_config cannot both be None")
        if not remote_config and local_config:
            # 只有本地配置，生成全量删除命令
            self._delete_full_by_config(task_data, local_config)
            self.local_data.delete_local_config(local_config.vlan_id)
            pass
        if remote_config and not local_config:
            # 只有远程配置，生成全量创建命令
            self._create_full_by_config(task_data, remote_config)
            self.local_data.update_local_config(remote_config)
            pass
        if remote_config and local_config:
            # 两个配置都存在，比对差异生成更新命令
            self._process_config_diff(task_data, remote_config, local_config)
            self.local_data.update_local_config(remote_config)
        return True

    def _create_full_by_config(self, task_data: TaskData, config: SubnetConfigParams):
        """创建所有配置

        Args:
            task_data: 任务数据
            config: 配置数据
        """
        self._create_subnet(task_data, config)
        self._create_monnet(task_data, config)
        self._create_eipnet(task_data, config)
    pass

    def _delete_full_by_config(self, task_data: TaskData, config: SubnetConfigParams) -> bool:
        """删除所有配置

        Args:
            task_data: 任务数据
            config: 配置数据
        """
        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        # 清除 nftables 规则
        self._execute_single_command(
            f"ip netns exec {namespace} nft flush ruleset || true")

        # 删除各个网络配置
        # self._delete_eipnet(task_data, config)
        # self._delete_monnet(task_data, config)

        # 清除设备
        if not self._delete_net_device(task_data, "br-eip", self._get_device_name("eip", "host", vlan_id)):
            return False
        if not self._delete_net_device(task_data, "br-mon", self._get_device_name("mon", "host", vlan_id)):
            return False
        if not self._delete_net_device(task_data, "br-vlan", self._get_device_name("lan", "host", vlan_id)):
            return False

        # 清除命名空间
        if not self._delete_netns(task_data, namespace):
            return False
        return True

    # 创建子网

    def _create_subnet(self, task_data: TaskData, config: SubnetConfigParams) -> bool:
        """
        创建子网

        Args:
            task_data: 任务数据
            config: 配置数据
        """
        subnet_config = config.subnet
        if not subnet_config:
            # 没有子网配置
            return True
        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        if not self._create_netns(task_data, namespace):
            return False
        # 创建设备
        bridge = "br-vlan"
        prefix = "lan"
        gateway_ip = subnet_config.gateway_ip
        if not self._create_net_device(task_data, namespace, bridge, prefix, vlan_id, gateway_ip):
            return False
        return True

    def _create_monnet(self, task_data: TaskData, config: SubnetConfigParams) -> bool:
        """创建监控网络

        Args:
            task_data: 任务数据
            config: 配置数据
        """
        monnet_config = config.monnet
        if not monnet_config:
            # 没有监控网络配置
            return True
        vlan_id = config.vlan_id
        mon_ip = monnet_config.mon_ip
        namespace = self._get_namespace(vlan_id)
        # 创建监控设备
        if not self._create_net_device(task_data, namespace, "br-mon", "mon", vlan_id, mon_ip):
            return False
        # 创建 nftables
        table_name = self._get_nft_table_name(vlan_id)
        if not self._create_nft_table(task_data, namespace, table_name):
            return False
        # 创建 PREROUTING 链
        pre_chain = "pre_monnet"
        if not self._create_nft_chain(task_data, namespace, table_name, pre_chain, "prerouting", -100):
            return False
        # 创建 POSTROUTING 链
        post_chain = "pos_monnet"
        if not self._create_nft_chain(task_data, namespace, table_name, post_chain, "postrouting", 100):
            return False
        # 创建 nftables 规则
        mon_ip = monnet_config.mon_ip
        mon_m_ip = monnet_config.mon_m_ip
        mon_m_port = monnet_config.mon_m_ports
        ext_m_ports = monnet_config.ext_m_ports
        if not self._add_nft_rule(task_data, namespace, table_name, pre_chain,
                                  f"ip daddr *************** tcp dport 443 counter dnat to {mon_m_ip}:{mon_m_port}", "monnet"):
            return False
        if not self._add_nft_rule(task_data, namespace, table_name, post_chain,
                                  f"ip daddr {mon_m_ip} tcp dport {mon_m_port} counter snat to {mon_ip}", "monnet"):
            return False
        return True

    def _delete_monnet(self, task_data: TaskData, config: SubnetConfigParams) -> bool:
        """删除监控网络

        Args:
            task_data: 任务数据
            config: 配置数据
        """
        monnet_config = config.monnet
        if not monnet_config:
            # 没有监控网络配置
            return True
        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)
        # 删除链
        table_name = self._get_nft_table_name(vlan_id)
        pre_chain = "pre_monnet"
        post_chain = "pos_monnet"
        if not self._delete_nft_chain(task_data, namespace, table_name, pre_chain):
            return False
        if not self._delete_nft_chain(task_data, namespace, table_name, post_chain):
            return False
        # 删除设备
        if not self._delete_net_device(task_data, namespace, "br-mon", "mon", vlan_id):
            return False
        return True

    # 创建命名空间
    def _create_netns(self, task_data: TaskData, namespace: str) -> bool:
        """创建命名空间

        Args:
            task_data: 任务数据
            namespace: 命名空间名称

        """
        if not self._execute_check_command(f"ip netns list | grep -w {namespace}"):
            # 不存在则创建
            cmd = f"ip netns add {namespace}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 删除命名空间
    def _delete_netns(self, task_data: TaskData, namespace: str) -> bool:
        """删除命名空间

        Args:
            task_data: 任务数据
            namespace: 命名空间名称

        """
        if self._execute_check_command(f"ip netns list | grep -w {namespace}"):
            cmd = f"ip netns delete {namespace}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 创建设备
    def _create_net_device(self, task_data: TaskData, namespace: str, bridge: str, prefix: str, vlan_id: int, ip_address: str) -> bool:
        """创建网络设备

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            bridge: 网桥名称
            prefix: 设备名称前缀
            vlan_id: VLAN ID
            ip_address: IP 地址, 可选

        """
        device = self._get_device_name(prefix, "host", vlan_id)
        peer_device = self._get_device_name(prefix, "ns", vlan_id)
        # 创建 veth pair设备
        if not self._execute_check_command(f"ip link show dev {device}"):
            cmd = f"ip link add {device} type veth peer name {peer_device}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
            # 移动 peer 设备到命名空间
            cmd = f"ip link set {peer_device} netns {namespace}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
            # 添加设备到网桥, 通过 --may-exist 自动判断设备是否存在, 不需要代码判断
            cmd = f"ovs-vsctl --may-exist add-port {bridge} {device} tag={vlan_id}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
            # 启动设备
            self._execute_single_command(f"ip link set {device} up")
            self._execute_single_command(f"ip netns exec {namespace} ip link set {peer_device} up")
        # 设备添加 IP
        if ip_address and not self._execute_check_command(
                f"ip netns exec {namespace} ip addr show dev {peer_device} | grep -w {ip_address}"):
            cmd = f"ip addr add {ip_address} dev {device}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 删除设备
    def _delete_net_device(self, task_data: TaskData, namespace: str, bridge: str, prefix: str, vlan_id: int) -> bool:
        """删除网络设备

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            bridge: 网桥名称
            prefix: 设备名称前缀
            vlan_id: VLAN ID

        """
        device = self._get_device_name(prefix, "host", vlan_id)
        # 删除 ovs 端口, --if-exists 自动判断端口是否存在, 不需要代码判断
        cmd = f"ovs-vsctl --if-exists del-port {bridge} {device}"
        self._append_task_cmd(task_data, cmd)
        success, _, err = self._execute_single_command(cmd)
        if not success:
            return self._task_error(task_data, cmd, err)

        # 删除设备
        if self._execute_check_command(f"ip link show {device}"):
            cmd = f"ip link del {device}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
            
        # 删除命名空间中的设备
        peer_device = self._get_device_name(prefix, "ns", vlan_id)
        if self._execute_check_command(f"ip netns exec {namespace} ip link show {peer_device}"):
            cmd = f"ip netns exec {namespace} ip link del {peer_device}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 创建 nft 表
    def _create_nft_table(self, task_data: TaskData, namespace: str, table_name: str) -> bool:
        """创建 nft 表

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称

        """
        if not self._execute_check_command(f"ip netns exec {namespace} nft list table ip {table_name}"):
            cmd = f"ip netns exec {namespace} nft add table ip {table_name}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 删除 nft 表
    def _delete_nft_table(self, task_data: TaskData, namespace: str, table_name: str) -> bool:
        """删除 nft 表

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称

        """
        if self._execute_check_command(f"ip netns exec {namespace} nft list table ip {table_name}"):
            cmd = f"ip netns exec {namespace} nft delete table ip {table_name}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 创建 nft 链
    def _create_nft_chain(self, task_data: TaskData, namespace: str, table_name: str, chain: str, hook: str, priority: Optional[int] = None) -> bool:
        """创建 nft 链

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称
            hook: nft 链钩子 (prerouting | postrouting)
            priority: nft 链优先级, 选填; hook 为 prerouting 时, 默认为 -100, hook 为 postrouting 时, 默认为 100
        """
        if priority is None:
            if hook == "prerouting":
                priority = -100
            elif hook == "postrouting":
                priority = 100
            else:
                self.logger.error(f"Invalid hook {hook} for nft chain")
                return False
        if not self._execute_check_command(f"ip netns exec {namespace} nft list chain ip {table_name} {chain}"):
            cmd = f"ip netns exec {namespace} nft add chain ip {table_name} {chain} {{ type nat hook {hook} priority {priority} \\; }}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 删除 nft 链
    def _delete_nft_chain(self, task_data: TaskData, namespace: str, table_name: str, chain: str) -> bool:
        """删除指定的 nft 链

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称

        """
        if self._execute_check_command(f"ip netns exec {namespace} nft list chain ip {table_name} {chain}"):
            cmd = f"ip netns exec {namespace} nft delete chain ip {table_name} {chain}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 添加 nft 规则
    def _add_nft_rule(self, task_data: TaskData, namespace: str, table_name: str, chain: str, rule: str, comment: str) -> bool:
        """添加一条 nft 规则

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称
            rule: nft 规则
            comment: nft 规则注释, 用于匹配规则, 避免重复
        """
        if not self._execute_check_command(f'ip netns exec {namespace} nft list ruleset | grep "comment \"{comment}\""'):
            cmd = f"ip netns exec {namespace} nft add rule ip {table_name} {chain} {rule} comment \"{comment}\""
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # 删除 nft 规则
    def _del_nft_rule(self, task_data: TaskData, namespace: str, table_name: str, chain: str, rule: str, comment: str) -> bool:
        """删除一条 nft 规则

        Args:
            task_data: 任务数据
            namespace: 命名空间名称
            table_name: nft 表名称
            chain: nft 链名称
            rule: nft 规则
            comment: nft 规则注释, 用于匹配规则, 必须要和添加时的注释完全一致
        """
        success, _, err = self._execute_single_command(cmd)
        cmd = f"ip netns exec {namespace} nft -a list chain ip {table_name} {chain} | grep 'comment \"{comment}\"' | awk '{{print $NF}}'"
        success, handle, _ = self._execute_single_command(cmd)
        if success:
            cmd = f"ip netns exec {namespace} nft delete rule ip {table_name} {chain} handle {handle}"
            self._append_task_cmd(task_data, cmd)
            success, _, err = self._execute_single_command(cmd)
            if not success:
                return self._task_error(task_data, cmd, err)
        return True

    # def _configs_differ(self, remote_obj: Any, local_obj: Any) -> bool:
    #     """比较两个配置对象是否不同"""
    #     if remote_obj is None and local_obj is None:
    #         return False
    #     if remote_obj is None or local_obj is None:
    #         return True

    #     # 转换为字典进行比较
    #     if hasattr(remote_obj, 'to_dict'):
    #         remote_dict = remote_obj.to_dict()
    #     else:
    #         remote_dict = remote_obj.__dict__ if hasattr(remote_obj, '__dict__') else remote_obj

    #     if hasattr(local_obj, 'to_dict'):
    #         local_dict = local_obj.to_dict()
    #     else:
    #         local_dict = local_obj.__dict__ if hasattr(local_obj, '__dict__') else local_obj

    #     return remote_dict != local_dict

    # def _eip_mounts_differ(self, remote_mounts: List, local_mounts: List) -> bool:
    #     """比较 EIP 挂载列表是否不同"""
    #     if len(remote_mounts) != len(local_mounts):
    #         return True

    #     # 转换为可比较的格式
    #     remote_set = set()
    #     for mount in remote_mounts:
    #         if hasattr(mount, 'to_dict'):
    #             mount_dict = mount.to_dict()
    #         else:
    #             mount_dict = mount.__dict__ if hasattr(mount, '__dict__') else mount
    #         remote_set.add(json.dumps(mount_dict, sort_keys=True))

    #     local_set = set()
    #     for mount in local_mounts:
    #         if hasattr(mount, 'to_dict'):
    #             mount_dict = mount.to_dict()
    #         else:
    #             mount_dict = mount.__dict__ if hasattr(mount, '__dict__') else mount
    #         local_set.add(json.dumps(mount_dict, sort_keys=True))

    #     return remote_set != local_set

    # def _generate_subnet_create_commands(self, subnet_config, vlan_id: str, namespace: str) -> List[str]:
    #     """生成子网创建命令"""
    #     commands = []

    #     # 创建 LAN veth pair
    #     commands.extend([
    #         f"ip link add v-lan-host-{vlan_id} type veth peer name v-lan-ns-{vlan_id}",
    #         f"ip link set v-lan-ns-{vlan_id} netns {namespace}",
    #         f"ip netns exec {namespace} ip addr add {subnet_config.gateway_ip}/24 dev v-lan-ns-{vlan_id}",
    #         f"ovs-vsctl add-port br-vlan v-lan-host-{vlan_id} tag={vlan_id}",
    #         f"ip link set v-lan-host-{vlan_id} up",
    #         f"ip netns exec {namespace} ip link set v-lan-ns-{vlan_id} up"
    #     ])

    #     return commands

    # def _generate_monnet_create_commands(self, monnet_config, vlan_id: str, namespace: str) -> List[str]:
    #     """生成监控网络创建命令"""
    #     commands = []

    #     # 创建监控 veth pair
    #     commands.extend([
    #         f"ip link add v-mon-host-{vlan_id} type veth peer name v-mon-ns-{vlan_id}",
    #         f"ip link set v-mon-ns-{vlan_id} netns {namespace}",
    #         f"ip netns exec {namespace} ip addr add {monnet_config.mon_ip}/24 dev v-mon-ns-{vlan_id}",
    #         f"ovs-vsctl add-port br-mon v-mon-host-{vlan_id}",
    #         f"ip link set v-mon-host-{vlan_id} up",
    #         f"ip netns exec {namespace} ip link set v-mon-ns-{vlan_id} up"
    #     ])

    #     return commands

    # def _generate_eip_create_commands(self, eip_mount, vlan_id: str, namespace: str, all_eip_mounts: List) -> List[str]:
    #     """生成 EIP 创建命令"""
    #     commands = []

    #     # 检查是否是第一个 EIP（需要创建设备和默认路由）
    #     is_first_eip = len([m for m in all_eip_mounts if m != eip_mount]) == 0

    #     if is_first_eip:
    #         # 创建 EIP veth pair
    #         commands.extend([
    #             f"ip link add v-eip-host-{vlan_id} type veth peer name v-eip-ns-{vlan_id}",
    #             f"ip link set v-eip-ns-{vlan_id} netns {namespace}",
    #             f"ovs-vsctl add-port br-eip v-eip-host-{vlan_id}",
    #             f"ip link set v-eip-host-{vlan_id} up",
    #             f"ip netns exec {namespace} ip link set v-eip-ns-{vlan_id} up"
    #         ])

    #     # 添加 EIP 地址
    #     commands.append(f"ip netns exec {namespace} ip addr add {eip_mount.eip}/24 dev v-eip-ns-{vlan_id}")

    #     # 如果是第一个 EIP，添加默认路由
    #     if is_first_eip:
    #         commands.append(f"ip netns exec {namespace} ip route add default via {eip_mount.gateway_ip} dev v-eip-ns-{vlan_id}")

    #     return commands

    # def _generate_eip_delete_commands(self, vlan_id: str, namespace: str, eip_mount, all_eip_mounts: List) -> List[str]:
    #     """生成 EIP 删除命令"""
    #     commands = []

    #     # 删除 EIP 地址
    #     commands.append(f"ip netns exec {namespace} ip addr del {eip_mount.eip}/24 dev v-eip-ns-{vlan_id} 2>/dev/null || true")

    #     # 检查是否是最后一个 EIP（需要删除设备和默认路由）
    #     other_eips = [m for m in all_eip_mounts if m != eip_mount]
    #     is_last_eip = len(other_eips) == 0

    #     if is_last_eip:
    #         # 删除默认路由和设备
    #         commands.extend([
    #             f"ip netns exec {namespace} ip route del default 2>/dev/null || true",
    #             f"ovs-vsctl del-port br-eip v-eip-host-{vlan_id} 2>/dev/null || true",
    #             f"ip link delete v-eip-host-{vlan_id} 2>/dev/null || true"
    #         ])

    #     return commands

    # def _generate_eip_diff_commands(self, remote_mounts: List, local_mounts: List, vlan_id: str, namespace: str) -> List[str]:
    #     """生成 EIP 差异命令"""
    #     commands = []

    #     # 找出需要删除的 EIP
    #     remote_eips = {(m.eip, m.internal_ip) for m in remote_mounts}
    #     local_eips = {(m.eip, m.internal_ip) for m in local_mounts}

    #     eips_to_delete = local_eips - remote_eips
    #     eips_to_add = remote_eips - local_eips

    #     # 删除不需要的 EIP
    #     for eip_tuple in eips_to_delete:
    #         eip_mount = next(m for m in local_mounts if (m.eip, m.internal_ip) == eip_tuple)
    #         commands.extend(self._generate_eip_delete_commands(vlan_id, namespace, eip_mount, local_mounts))

    #     # 添加新的 EIP
    #     for eip_tuple in eips_to_add:
    #         eip_mount = next(m for m in remote_mounts if (m.eip, m.internal_ip) == eip_tuple)
    #         commands.extend(self._generate_eip_create_commands(eip_mount, vlan_id, namespace, remote_mounts))

    #     return commands

    # def _generate_nft_create_commands(self, config: SubnetConfigParams, namespace: str) -> List[str]:
    #     """生成 NFT 规则创建命令"""
    #     commands = []

    #     # 创建 NAT 表和链
    #     commands.extend([
    #         f"ip netns exec {namespace} nft add table ip nat",
    #         f"ip netns exec {namespace} nft add chain ip nat PREROUTING {{ type nat hook prerouting priority -100 \\; }}",
    #         f"ip netns exec {namespace} nft add chain ip nat POSTROUTING {{ type nat hook postrouting priority 100 \\; }}"
    #     ])

    #     # 添加基础规则
    #     if config.subnet and config.monnet:
    #         # 子网内部 SNAT 规则
    #         commands.append(f"ip netns exec {namespace} nft add rule ip nat POSTROUTING ip saddr {config.subnet.pre_ip}.0/24 ip daddr {config.subnet.pre_ip}.0/24 counter snat to {config.subnet.gateway_ip}")

    #         # 监控服务 DNAT 规则
    #         commands.append(f"ip netns exec {namespace} nft add rule ip nat PREROUTING ip daddr *************** tcp dport 443 counter dnat to {config.monnet.mon_m_ip}:{config.monnet.mon_m_ports}")

    #         # 监控服务 SNAT 规则
    #         commands.append(f"ip netns exec {namespace} nft add rule ip nat POSTROUTING ip daddr {config.monnet.mon_m_ip} tcp dport {config.monnet.mon_m_ports} counter snat to {config.monnet.mon_ip}")

    #         # 扩展端口 SNAT 规则
    #         for ext_port in config.monnet.ext_m_ports:
    #             commands.append(f"ip netns exec {namespace} nft add rule ip nat POSTROUTING ip daddr {config.monnet.mon_m_ip} tcp dport {ext_port} counter snat to {config.monnet.mon_ip}")

    #     # EIP 相关规则
    #     for eip_mount in config.eip_mount:
    #         # EIP DNAT 规则
    #         commands.append(f"ip netns exec {namespace} nft add rule ip nat PREROUTING ip daddr {eip_mount.eip} counter dnat to {eip_mount.internal_ip}")

    #         # EIP SNAT 规则
    #         commands.append(f"ip netns exec {namespace} nft add rule ip nat POSTROUTING ip saddr {eip_mount.internal_ip} ip daddr != {{ 10.0.0.0/8, **********/12, ***********/24, ***********/24 }} counter snat to {eip_mount.eip}")

    #     return commands

    # def _execute_commands(self, commands: List[str], task_data: Optional[TaskData] = None) -> bool:
    #     """执行命令列表

    #     Args:
    #         commands: 命令列表
    #         task_data: 任务数据（可选）

    #     Returns:
    #         True if all commands executed successfully
    #     """
    #     executed_commands = []

    #     try:
    #         for i, cmd in enumerate(commands):
    #             self.logger.debug(f"Executing command {i+1}/{len(commands)}: {cmd}")

    #             # 检查命令执行前置条件
    #             if not self._check_command_precondition(cmd):
    #                 self.logger.debug(f"Skipping command due to precondition: {cmd}")
    #                 continue

    #             if not self.dry_run:
    #                 success, output, error = self._execute_single_command(cmd)
    #                 if not success:
    #                     # 命令执行失败
    #                     error_msg = f"Command failed: {cmd}, Error: {error}"
    #                     self.logger.error(error_msg)
    #                     if task_data:
    #                         task_data.status = TaskStatus.ERROR
    #                         task_data.message = error_msg
    #                         task_data.cmds = executed_commands
    #                     return False

    #             executed_commands.append(cmd)

    #             # 更新任务数据
    #             if task_data:
    #                 task_data.cmds = executed_commands.copy()

    #         # 所有命令执行完成
    #         if task_data:
    #             task_data.status = TaskStatus.COMPLETE
    #             task_data.message = "ok"
    #             task_data.cmds = executed_commands

    #         self.logger.info(f"Successfully executed {len(executed_commands)} commands")
    #         return True

    #     except Exception as e:
    #         error_msg = f"Error executing commands: {e}"
    #         self.logger.error(error_msg)
    #         if task_data:
    #             task_data.status = TaskStatus.ERROR
    #             task_data.message = error_msg
    #             task_data.cmds = executed_commands
    #         return False

    def _task_error(self, task_data: TaskData, cmd: str, error: str) -> bool:
        error_msg = f"cmd [{cmd}] execute error: {error}"
        self.logger.error(error_msg)
        if task_data:
            task_data.status = TaskStatus.FAILED
            task_data.message = error_msg
        return False

    def _check_command_precondition(self, cmd: str) -> bool:
        """检查命令执行前置条件

        Args:
            cmd: 要执行的命令

        Returns:
            True if command should be executed
        """
        try:
            # 检查命名空间创建
            if "ip netns add" in cmd:
                namespace = cmd.split()[-1]
                check_cmd = f"ip netns list | grep -q '^{namespace}$'"
                result = subprocess.run(
                    check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    self.logger.debug(
                        f"Namespace {namespace} already exists, skipping creation")
                    return False

            # 检查设备删除
            elif "ip link delete" in cmd:
                device = cmd.split()[-1]
                check_cmd = f"ip link show {device}"
                result = subprocess.run(
                    check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                if result.returncode != 0:
                    self.logger.debug(
                        f"Device {device} does not exist, skipping deletion")
                    return False

            # 检查 OVS 端口删除
            elif "ovs-vsctl del-port" in cmd and "2>/dev/null || true" not in cmd:
                parts = cmd.split()
                if len(parts) >= 4:
                    bridge = parts[2]
                    port = parts[3]
                    check_cmd = f"ovs-vsctl list-ports {bridge} | grep -q '^{port}$'"
                    result = subprocess.run(
                        check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode != 0:
                        self.logger.debug(
                            f"Port {port} does not exist on bridge {bridge}, skipping deletion")
                        return False

            # 检查命名空间删除
            elif "ip netns delete" in cmd:
                namespace = cmd.split()[-1]
                check_cmd = f"ip netns list | grep -q '^{namespace}$'"
                result = subprocess.run(
                    check_cmd, shell=True, capture_output=True, text=True, timeout=10)
                if result.returncode != 0:
                    self.logger.debug(
                        f"Namespace {namespace} does not exist, skipping deletion")
                    return False

            return True

        except Exception as e:
            self.logger.warning(
                f"Error checking precondition for command '{cmd}': {e}")
            return True  # 如果检查失败，继续执行命令

    def _execute_check_command(self, cmd: str) -> bool:
        """执行单个命令

        Args:
            cmd: 要执行的命令
        Returns:
            Tuple of (success, output, error)
        """
        try:
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=10
            )

            success = result.returncode == 0

            if success:
                self.logger.debug(f"Command succeeded: {cmd}")
            else:
                self.logger.error(
                    f"Command failed: {cmd}, Return code: {result.returncode}, Error: {result.stderr.strip()}")

            return success
        except Exception as e:
            self.logger.error(f"Command execution error: {e}")
            return False

    def _execute_single_command(self, cmd: str) -> Tuple[bool, str, str]:
        """执行单个命令

        Args:
            cmd: 要执行的命令

        Returns:
            Tuple of (success, output, error)
        """
        try:
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            success = result.returncode == 0
            output = result.stdout.strip()
            error = result.stderr.strip()

            if success:
                self.logger.debug(f"Command succeeded: {cmd}")
            else:
                self.logger.error(
                    f"Command failed: {cmd}, Return code: {result.returncode}, Error: {error}")

            return success, output, error

        except subprocess.TimeoutExpired:
            error_msg = f"Command timeout: {cmd}"
            self.logger.error(error_msg)
            return False, "", error_msg
        except Exception as e:
            error_msg = f"Command execution error: {e}"
            self.logger.error(error_msg)
            return False, "", error_msg

    def _append_task_cmd(self, task_data: TaskData, cmd: str):
        if task_data:
            task_data.append_cmd(cmd)

    def _get_namespace(self, vlan_id: int) -> str:
        """获取命名空间名称"""
        return f"ns-vlan{vlan_id}"

    def _get_device_name(self, prefix: str, infix: str, vlan_id: int) -> str:
        """获取设备名称"""
        return f"v-{prefix}-{infix}-{vlan_id}"

    def _get_nft_table_name(self, vlan_id: int) -> str:
        """获取 nftables 表名称"""
        return f"nat-{vlan_id}"

    def _process_config_diff(self, task_data: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理配置差异

        Args:
            task_data: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        try:
            # 1. 比对 subnet 配置
            if not self._process_subnet_diff(task_data, remote_config, local_config):
                return False

            # 2. 比对 monnet 配置
            if not self._process_monnet_diff(task_data, remote_config, local_config):
                return False

            # 3. 比对 eipnet 配置
            if not self._process_eipnet_diff(task_data, remote_config, local_config):
                return False

            return True

        except Exception as e:
            error_msg = f"Error processing config diff: {e}"
            self.logger.error(error_msg)
            if task_data:
                task_data.status = TaskStatus.ERROR
                task_data.message = error_msg
            return False

    def _process_subnet_diff(self, task_data: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理子网配置差异

        Args:
            task_data: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        remote_subnet = remote_config.subnet
        local_subnet = local_config.subnet

        # 比对逻辑
        if not remote_subnet and local_subnet:
            # 远程为 null，本地不为 null，生成删除命令
            self.logger.info("Subnet: remote is null, local exists - deleting subnet")
            return self._delete_subnet(task_data, local_config)

        elif remote_subnet and not local_subnet:
            # 远程不为 null，本地为 null，生成创建命令
            self.logger.info("Subnet: remote exists, local is null - creating subnet")
            return self._create_subnet(task_data, remote_config)

        elif remote_subnet and local_subnet:
            # 两者都不为 null，检查内容是否一致
            if not self._subnet_configs_equal(remote_subnet, local_subnet):
                # 内容不一致，先删除后创建
                self.logger.info("Subnet: configs differ - recreating subnet")
                if not self._delete_subnet(task_data, local_config):
                    return False
                return self._create_subnet(task_data, remote_config)
            else:
                self.logger.info("Subnet: configs are identical - no changes needed")

        # 两者都为 null 或内容相同，无需操作
        return True

    def _process_monnet_diff(self, task_data: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理监控网络配置差异

        Args:
            task_data: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        remote_monnet = remote_config.monnet
        local_monnet = local_config.monnet

        # 比对逻辑
        if not remote_monnet and local_monnet:
            # 远程为 null，本地不为 null，生成删除命令
            self.logger.info("Monnet: remote is null, local exists - deleting monnet")
            return self._delete_monnet(task_data, local_config)

        elif remote_monnet and not local_monnet:
            # 远程不为 null，本地为 null，生成创建命令
            self.logger.info("Monnet: remote exists, local is null - creating monnet")
            return self._create_monnet(task_data, remote_config)

        elif remote_monnet and local_monnet:
            # 两者都不为 null，检查内容是否一致
            if not self._monnet_configs_equal(remote_monnet, local_monnet):
                # 内容不一致，先删除后创建
                self.logger.info("Monnet: configs differ - recreating monnet")
                if not self._delete_monnet(task_data, local_config):
                    return False
                return self._create_monnet(task_data, remote_config)
            else:
                self.logger.info("Monnet: configs are identical - no changes needed")

        # 两者都为 null 或内容相同，无需操作
        return True

    def _process_eipnet_diff(self, task_data: TaskData, remote_config: SubnetConfigParams, local_config: SubnetConfigParams) -> bool:
        """处理 EIP 网络配置差异

        Args:
            task_data: 任务数据
            remote_config: 远程配置
            local_config: 本地配置

        Returns:
            True if successful
        """
        remote_eipnet = remote_config.eipnet
        local_eipnet = local_config.eipnet

        # 比对逻辑
        if not remote_eipnet and local_eipnet:
            # 远程为 null，本地不为 null，生成删除命令
            self.logger.info("Eipnet: remote is null, local exists - deleting eipnet")
            return self._delete_eipnet(task_data, local_config)

        elif remote_eipnet and not local_eipnet:
            # 远程不为 null，本地为 null，生成创建命令
            self.logger.info("Eipnet: remote exists, local is null - creating eipnet")
            return self._create_eipnet(task_data, remote_config)

        elif remote_eipnet and local_eipnet:
            # 两者都不为 null，检查内容是否一致
            if not self._eipnet_configs_equal(remote_eipnet, local_eipnet):
                # 内容不一致，先删除后创建
                self.logger.info("Eipnet: configs differ - recreating eipnet")
                if not self._delete_eipnet(task_data, local_config):
                    return False
                return self._create_eipnet(task_data, remote_config)
            else:
                self.logger.info("Eipnet: configs are identical - no changes needed")

        # 两者都为 null 或内容相同，无需操作
        return True

    def _subnet_configs_equal(self, config1, config2) -> bool:
        """比较两个子网配置是否相等"""
        if not config1 or not config2:
            return config1 == config2

        return (config1.gateway_ip == config2.gateway_ip and
                config1.pre_ip == config2.pre_ip)

    def _monnet_configs_equal(self, config1, config2) -> bool:
        """比较两个监控网络配置是否相等"""
        if not config1 or not config2:
            return config1 == config2

        return (config1.mon_ip == config2.mon_ip and
                config1.mon_m_ip == config2.mon_m_ip and
                config1.mon_m_ports == config2.mon_m_ports and
                config1.ext_m_ports == config2.ext_m_ports)

    def _eipnet_configs_equal(self, config1, config2) -> bool:
        """比较两个 EIP 网络配置是否相等"""
        if not config1 or not config2:
            return config1 == config2

        # 比较 gateway_ip
        if config1.gateway_ip != config2.gateway_ip:
            return False

        # 比较 mounts 列表
        if len(config1.mounts) != len(config2.mounts):
            return False

        # 将 mounts 列表转换为可比较的集合
        mounts1_set = {(m.eip, m.internal_ip) for m in config1.mounts}
        mounts2_set = {(m.eip, m.internal_ip) for m in config2.mounts}

        return mounts1_set == mounts2_set

    def _delete_subnet(self, task_data: TaskData, config: SubnetConfigParams) -> bool:
        """删除子网配置"""
        if not config.subnet:
            return True

        vlan_id = config.vlan_id
        # 删除子网设备
        return self._delete_net_device(task_data, "br-vlan", self._get_device_name("lan", "host", vlan_id))

    def _create_eipnet(self, task_data: TaskData, config: SubnetConfigParams) -> bool:
        """创建 EIP 网络配置"""
        eipnet_config = config.eipnet
        if not eipnet_config or not eipnet_config.mounts:
            return True

        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)

        # 创建 nft 表和链
        table_name = self._get_nft_table_name(vlan_id)
        if not self._create_nft_table(task_data, namespace, table_name):
            return False

        pre_chain_name = "prerouting"
        if not self._create_nft_chain(task_data, namespace, table_name, pre_chain_name, "prerouting", -100):
            return False

        post_chain_name = "postrouting"
        if not self._create_nft_chain(task_data, namespace, table_name, post_chain_name, "postrouting", 100):
            return False

        # 处理每个 EIP 挂载
        device = self._get_device_name('eip', 'ns', vlan_id)
        gateway_ip = eipnet_config.gateway_ip

        for mount in eipnet_config.mounts:
            eip = mount.eip
            internal_ip = mount.internal_ip

            # 创建 EIP 设备（如果已存在则只设置IP）
            if not self._create_net_device(task_data, namespace, "br-eip", "eip", vlan_id, eip):
                return False

            # 添加默认路由
            if not self._execute_check_command(f"ip netns exec {namespace} ip route show | grep -w '{gateway_ip}'"):
                cmd = f"ip netns exec {namespace} ip route add default via {gateway_ip} dev {device}"
                self._append_task_cmd(task_data, cmd)
                success, _, err = self._execute_single_command(cmd)
                if not success:
                    return self._task_error(task_data, cmd, err)

            # 添加 nft 规则
            if not self._add_nft_rule(task_data, namespace, table_name, pre_chain_name,
                                      f"ip daddr {eip} counter dnat to {internal_ip}",
                                      comment=f"{eip} to {internal_ip}"):
                return False

            if not self._add_nft_rule(task_data, namespace, table_name, post_chain_name,
                                      f"ip saddr {internal_ip} ip daddr != {{ 10.0.0.0/8, **********/12, ***********/24, ***********/24 }} snat to {eip}",
                                      comment=f"{internal_ip} to {eip}"):
                return False

        return True

    def _delete_eipnet(self, task_data: TaskData, config: SubnetConfigParams) -> bool:
        """删除 EIP 网络配置"""
        eipnet_config = config.eipnet
        if not eipnet_config:
            return True

        vlan_id = config.vlan_id
        namespace = self._get_namespace(vlan_id)

        # 删除 nft 规则和表
        table_name = self._get_nft_table_name(vlan_id)
        cmd = f"ip netns exec {namespace} nft delete table ip {table_name} 2>/dev/null || true"
        self._append_task_cmd(task_data, cmd)
        success, _, err = self._execute_single_command(cmd)
        if not success:
            self.logger.warning(f"Failed to delete nft table: {err}")

        # 删除 EIP 设备
        return self._delete_net_device(task_data, "br-eip", self._get_device_name("eip", "host", vlan_id))

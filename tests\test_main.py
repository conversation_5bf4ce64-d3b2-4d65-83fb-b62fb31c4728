"""
Tests for the main module.
"""

import pytest
from v_switch.main import main


def test_main_function():
    """Test that main function runs without error."""
    # This is a basic test - you can expand it based on your needs
    try:
        main()
        assert True
    except Exception as e:
        pytest.fail(f"main() raised an exception: {e}")


def test_main_function_output(capsys):
    """Test that main function produces expected output."""
    main()
    captured = capsys.readouterr()
    assert "Hello from v-switch!" in captured.out

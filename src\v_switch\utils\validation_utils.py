"""
Validation utilities for v-switch.
"""

import re
import ipaddress
from typing import List, Optional, Dict, Any


class ValidationUtils:
    """Utilities for validating input data."""
    
    # Regular expressions for validation
    VLAN_ID_PATTERN = re.compile(r'^[0-9]+$')
    AGENT_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
    TENANT_ID_PATTERN = re.compile(r'^[a-zA-Z0-9_-]+$')
    
    @staticmethod
    def validate_ip_address(ip: str) -> bool:
        """Validate IP address format.
        
        Args:
            ip: IP address string
            
        Returns:
            True if valid
        """
        try:
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_ip_network(network: str) -> bool:
        """Validate IP network format (CIDR).
        
        Args:
            network: IP network string (e.g., "***********/24")
            
        Returns:
            True if valid
        """
        try:
            ipaddress.ip_network(network, strict=False)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_vlan_id(vlan_id: str) -> bool:
        """Validate VLAN ID format.
        
        Args:
            vlan_id: VLAN ID string
            
        Returns:
            True if valid
        """
        if not vlan_id or not ValidationUtils.VLAN_ID_PATTERN.match(vlan_id):
            return False
        
        try:
            vlan_num = int(vlan_id)
            return 1 <= vlan_num <= 4094  # Valid VLAN range
        except ValueError:
            return False
    
    @staticmethod
    def validate_agent_id(agent_id: str) -> bool:
        """Validate agent ID format.
        
        Args:
            agent_id: Agent ID string
            
        Returns:
            True if valid
        """
        if not agent_id:
            return False
        
        return (
            ValidationUtils.AGENT_ID_PATTERN.match(agent_id) and
            len(agent_id) >= 3 and
            len(agent_id) <= 64
        )
    
    @staticmethod
    def validate_tenant_id(tenant_id: str) -> bool:
        """Validate tenant ID format.
        
        Args:
            tenant_id: Tenant ID string
            
        Returns:
            True if valid
        """
        if not tenant_id:
            return False
        
        return (
            ValidationUtils.TENANT_ID_PATTERN.match(tenant_id) and
            len(tenant_id) >= 3 and
            len(tenant_id) <= 64
        )
    
    @staticmethod
    def validate_port(port: int) -> bool:
        """Validate port number.
        
        Args:
            port: Port number
            
        Returns:
            True if valid
        """
        return 1 <= port <= 65535
    
    @staticmethod
    def validate_timeout(timeout: int) -> bool:
        """Validate timeout value.
        
        Args:
            timeout: Timeout in seconds
            
        Returns:
            True if valid
        """
        return 1 <= timeout <= 3600  # 1 second to 1 hour
    
    @staticmethod
    def validate_heartbeat_interval(interval: int) -> bool:
        """Validate heartbeat interval.
        
        Args:
            interval: Interval in seconds
            
        Returns:
            True if valid
        """
        return 5 <= interval <= 300  # 5 seconds to 5 minutes
    
    @staticmethod
    def validate_shard_count(count: int) -> bool:
        """Validate shard count.
        
        Args:
            count: Number of shards
            
        Returns:
            True if valid
        """
        return 1 <= count <= 1000
    
    @staticmethod
    def validate_subnet_gateway_request(data: Dict[str, Any]) -> List[str]:
        """Validate subnet gateway creation request.
        
        Args:
            data: Request data
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required fields
        required_fields = ['tenant_id', 'vlan_id', 'subnet_gw_ip']
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f"Missing required field: {field}")
        
        # Validate field formats
        if 'tenant_id' in data and data['tenant_id']:
            if not ValidationUtils.validate_tenant_id(data['tenant_id']):
                errors.append("Invalid tenant_id format")
        
        if 'vlan_id' in data and data['vlan_id']:
            if not ValidationUtils.validate_vlan_id(str(data['vlan_id'])):
                errors.append("Invalid vlan_id format")
        
        if 'subnet_gw_ip' in data and data['subnet_gw_ip']:
            if not ValidationUtils.validate_ip_address(data['subnet_gw_ip']):
                errors.append("Invalid subnet_gw_ip format")
        
        return errors
    
    @staticmethod
    def validate_eip_request(data: Dict[str, Any]) -> List[str]:
        """Validate EIP creation request.
        
        Args:
            data: Request data
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required fields
        required_fields = ['tenant_id', 'vlan_id', 'eip', 'gateway_ip', 'internal_ip']
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f"Missing required field: {field}")
        
        # Validate field formats
        if 'tenant_id' in data and data['tenant_id']:
            if not ValidationUtils.validate_tenant_id(data['tenant_id']):
                errors.append("Invalid tenant_id format")
        
        if 'vlan_id' in data and data['vlan_id']:
            if not ValidationUtils.validate_vlan_id(str(data['vlan_id'])):
                errors.append("Invalid vlan_id format")
        
        # Validate IP addresses
        ip_fields = ['eip', 'gateway_ip', 'internal_ip']
        for field in ip_fields:
            if field in data and data[field]:
                if not ValidationUtils.validate_ip_address(data[field]):
                    errors.append(f"Invalid {field} format")
        
        return errors
    
    @staticmethod
    def validate_etcd_config(config: Dict[str, Any]) -> List[str]:
        """Validate ETCD configuration.
        
        Args:
            config: ETCD configuration
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required fields
        if 'host' not in config or not config['host']:
            errors.append("Missing ETCD host")
        
        if 'port' not in config:
            errors.append("Missing ETCD port")
        elif not ValidationUtils.validate_port(config['port']):
            errors.append("Invalid ETCD port")
        
        if 'timeout' in config and not ValidationUtils.validate_timeout(config['timeout']):
            errors.append("Invalid ETCD timeout")
        
        return errors
    
    @staticmethod
    def validate_agent_config(config: Dict[str, Any]) -> List[str]:
        """Validate agent configuration.
        
        Args:
            config: Agent configuration
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check agent ID
        if 'agent_id' not in config or not config['agent_id']:
            errors.append("Missing agent_id")
        elif not ValidationUtils.validate_agent_id(config['agent_id']):
            errors.append("Invalid agent_id format")
        
        # Check heartbeat interval
        if 'heartbeat_interval' in config:
            if not ValidationUtils.validate_heartbeat_interval(config['heartbeat_interval']):
                errors.append("Invalid heartbeat_interval")
        
        return errors
    
    @staticmethod
    def validate_server_config(config: Dict[str, Any]) -> List[str]:
        """Validate server configuration.
        
        Args:
            config: Server configuration
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check port
        if 'port' not in config:
            errors.append("Missing server port")
        elif not ValidationUtils.validate_port(config['port']):
            errors.append("Invalid server port")
        
        # Check shard count
        if 'shard_count' in config:
            if not ValidationUtils.validate_shard_count(config['shard_count']):
                errors.append("Invalid shard_count")
        
        return errors
    
    @staticmethod
    def sanitize_string(value: str, max_length: int = 255) -> str:
        """Sanitize string input.
        
        Args:
            value: Input string
            max_length: Maximum allowed length
            
        Returns:
            Sanitized string
        """
        if not isinstance(value, str):
            value = str(value)
        
        # Remove control characters and limit length
        sanitized = ''.join(char for char in value if ord(char) >= 32)
        return sanitized[:max_length]

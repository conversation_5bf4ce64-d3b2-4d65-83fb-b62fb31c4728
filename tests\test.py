import threading
from typing import Dict

class Test:

  def __init__(self):
    self._subnet_watch: Dict[str, str] = {}

  def add_key(self, key: str, value: str):
    self._subnet_watch[key] = value

  def print_keys(self):
    keys = self._subnet_watch.keys()
    print(f"keys: {keys}")
    for key in keys:
      print(key)


if __name__ == "__main__":
  # test = Test()
  # test.add_key("key1", "value1")
  # test.add_key("key2", "value2")
  # test.add_key("key2", "value2")
  # test.print_keys()
  ttt = {}
  ttt[2001] = "value1"
  ttt[2003] = "value2"
  ttt[2100] = "value2"
  print(ttt.keys())
  print(ttt.values())
  print(ttt)

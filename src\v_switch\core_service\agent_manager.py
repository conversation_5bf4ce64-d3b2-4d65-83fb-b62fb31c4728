"""
Agent management for v-switch core service.
"""

import json
import time
import logging
import threading
from typing import Dict, Any, List, Optional
from etcd3.events import PutEvent, DeleteEvent
from ..common.etcd_client import ETCDClient
from ..config.server_config import ServerConfig
from ..common.models import ServerMetadata, AgentInfo, AgentRegisterData


class AgentManager:
    """管理agent注册和分片分配。"""

    METADATA_KEY = "/network/server/metadata"
    REGISTER_PREFIX = "/network/agent/register/"
    TASK_PREFIX = "/network/agent/tasks/"

    def __init__(self, etcd_client: ETCDClient, config: ServerConfig):
        """初始化agent管理器。

        Args:
            etcd_client: ETCD client instance
            config: Server configuration
        """
        self.etcd_client = etcd_client
        self.config = config
        self.logger = logging.getLogger(__name__)
        self._metadata_cache = None
        self._monitoring = False
        self._monitor_thread = None
        
    def initialize_metadata(self) -> bool:
        """在etcd中初始化服务器元数据。

        Returns:
            True if successful
        """
        try:
            # 检查 /network/server/metadata 是否存在
            existing_data, _ = self.etcd_client.get_json(self.METADATA_KEY)
            if existing_data:
                self.logger.info("Server metadata already exists")
                self._metadata_cache = ServerMetadata.from_dict(existing_data)
                return True

            # 如果不存在则创建
            metadata = ServerMetadata(
                server={"shard_count": self.config.server.shard_count},
                agent=[]
            )

            success = self.etcd_client.put_json(self.METADATA_KEY, metadata.to_dict())
            if success:
                self.logger.info("Initialized server metadata")
                self._metadata_cache = metadata
            else:
                self.logger.error("Failed to initialize server metadata")

            return success

        except Exception as e:
            self.logger.error(f"Error initializing metadata: {e}")
            return False

    def check_agent_registration(self) -> bool:
        """启动时查询元数据中的agent列表，检查online字段，如果为false且shard不为空，则重新分配分片。

        Returns:
            True if successful
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                return False

            # 检查每个agent的状态
            for agent in metadata.agent:
                if not agent.online and agent.shard:
                    # 如果agent离线但有分片，重新分配分片
                    self.logger.info(f"Reassigning shards for offline agent {agent.agent_id}")
                    self._reassign_shards(agent.agent_id, agent.shard)
                    agent.shard = []

            # 更新元数据
            return self.update_metadata(metadata.to_dict())

        except Exception as e:
            self.logger.error(f"Error checking agent registration: {e}")
            return False

    def start_agent_monitoring(self) -> None:
        """启动agent监控，监听etcd的/network/agent/register/目录。"""
        try:
            self._monitoring = True
            self.logger.info("Starting agent registration monitoring")

            # 监听agent注册事件
            for event in self.etcd_client.watch_prefix(self.REGISTER_PREFIX):
                if not self._monitoring:
                    break

                self._handle_agent_event(event)

        except Exception as e:
            self.logger.error(f"Error in agent monitoring: {e}")
        finally:
            self._monitoring = False

    def stop_agent_monitoring(self) -> None:
        """停止agent监控。"""
        self._monitoring = False
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5.0)

    def _handle_agent_event(self, event) -> None:
        """处理agent注册事件。

        Args:
            event: ETCD watch event
        """
        try:
            if isinstance(event, DeleteEvent):
                # DELETE事件：修改metadata数据，对应agent的online字段设置为false
                agent_id = event.key.decode().replace(self.REGISTER_PREFIX, "")
                self._handle_agent_offline(agent_id)

            elif isinstance(event, PutEvent):
                # PUT事件：根据prev_kv判断status字段变化
                agent_id = event.key.decode().replace(self.REGISTER_PREFIX, "")
                current_data = json.loads(event.value.decode())

                prev_status = None
                if event.prev_kv:
                    prev_data = json.loads(event.prev_kv.value.decode())
                    prev_status = prev_data.get("status")

                current_status = current_data.get("status")

                if prev_status == "running" and current_status == "running":
                    # running -> running，不处理
                    return
                elif current_status == "error" or (prev_status == "running" and current_status == "error"):
                    # running -> error，执行逻辑同DELETE事件
                    self._handle_agent_offline(agent_id)
                elif (prev_status in ["error", None]) and current_status == "running":
                    # error -> running 或 null -> running，设置agent为在线
                    self._handle_agent_online(agent_id)

        except Exception as e:
            self.logger.error(f"Error handling agent event: {e}")

    def _handle_agent_offline(self, agent_id: str) -> None:
        """处理agent离线事件。

        Args:
            agent_id: Agent identifier
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                return

            # 查找对应的agent
            agent_info = None
            for agent in metadata.agent:
                if agent.agent_id == agent_id:
                    agent_info = agent
                    break

            if not agent_info:
                self.logger.warning(f"Agent {agent_id} not found in metadata")
                return

            # 设置为离线
            agent_info.online = False
            agent_info.offline_time = int(time.time())

            # 如果有分片，重新分配
            if agent_info.shard:
                self.logger.info(f"Reassigning shards for offline agent {agent_id}: {agent_info.shard}")
                self._reassign_shards(agent_id, agent_info.shard)
                agent_info.shard = []

            # 更新元数据
            self.update_metadata(metadata.to_dict())
            self.logger.info(f"Agent {agent_id} marked as offline")

        except Exception as e:
            self.logger.error(f"Error handling agent offline: {e}")

    def _handle_agent_online(self, agent_id: str) -> None:
        """处理agent上线事件。

        Args:
            agent_id: Agent identifier
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                return

            # 查找对应的agent
            agent_info = None
            for agent in metadata.agent:
                if agent.agent_id == agent_id:
                    agent_info = agent
                    break

            if not agent_info:
                # 如果agent不存在，创建新的agent信息
                agent_info = AgentInfo(
                    online=True,
                    agent_id=agent_id,
                    offline_time=0,
                    shard=[]
                )
                metadata.agent.append(agent_info)
                self.logger.info(f"Added new agent {agent_id} to metadata")
            else:
                # 设置为在线
                agent_info.online = True
                agent_info.offline_time = 0
                self.logger.info(f"Agent {agent_id} marked as online")

            # 更新元数据
            self.update_metadata(metadata.to_dict())

        except Exception as e:
            self.logger.error(f"Error handling agent online: {e}")

    def _reassign_shards(self, offline_agent_id: str, shards: List[str]) -> None:
        """重新分配分片给其他在线的agent。

        Args:
            offline_agent_id: Offline agent identifier
            shards: List of shards to reassign
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                return

            # 获取在线的agent列表
            online_agents = [agent for agent in metadata.agent if agent.online and agent.agent_id != offline_agent_id]

            if not online_agents:
                self.logger.warning("No online agents available for shard reassignment")
                return

            # 简单的轮询分配策略
            for i, shard in enumerate(shards):
                target_agent = online_agents[i % len(online_agents)]
                target_agent.shard.append(shard)
                self.logger.info(f"Reassigned shard {shard} from {offline_agent_id} to {target_agent.agent_id}")

        except Exception as e:
            self.logger.error(f"Error reassigning shards: {e}")

    def check_agent_status(self) -> bool:
        """检查所有agent的是否在线，是否有分配分片。
        Returns:
            True if all agents are healthy
        """
        try:
            metadata = self.get_metadata()
            if not metadata:
                self.logger.error("Failed to get metadata")
                return False

            # 获取在线的agent列表
            online_agents = [agent for agent in metadata.agent if agent.online]
            if not online_agents:
                self.logger.warning("No online agents found")
                return False

            # 检查每个agent的状态
            for agent in online_agents:
                if not agent.agent_id:
                    self.logger.error("Agent ID is missing in metadata")
                    return False
                # 检查分片是否分配
                if not agent.shard:
                    self.logger.warning(f"Agent {agent.agent_id} has no assigned shards")
                    return False
            return True

        except Exception as e:
            self.logger.error(f"Error checking agent status: {e}")
            return False


    def get_metadata(self) -> Optional[ServerMetadata]:
        """获取服务的元数据。

        Returns:
            Server metadata or None if not found
        """
        try:
            data, _ = self.etcd_client.get_json(self.METADATA_KEY)
            self.logger.debug(f"Got metadata: {data}")
            if data:
                metadata = ServerMetadata.from_dict(data)
                self._metadata_cache = metadata
                return metadata
            return None

        except Exception as e:
            self.logger.error(f"Error getting metadata: {e}")
            return None

    def update_metadata(self, metadata: Dict[str, Any]) -> bool:
        """更新服务的元数据。

        Args:
            metadata: Updated metadata

        Returns:
            True if successful
        """
        try:
            success = self.etcd_client.put_json(self.METADATA_KEY, metadata)
            if success:
                if isinstance(metadata, dict):
                    self._metadata_cache = ServerMetadata.from_dict(metadata)
                else:
                    self._metadata_cache = metadata
                self.logger.debug("Updated server metadata")
            else:
                self.logger.error("Failed to update server metadata")

            return success

        except Exception as e:
            self.logger.error(f"Error updating metadata: {e}")
            return False


    def start_task_monitoring(self) -> None:
        """启动agent任务监听，监听etcd: /network/agent/tasks/目录。"""
        try:
            self.logger.info("Starting agent task monitoring")

            # 监听agent任务事件
            for event in self.etcd_client.watch_prefix(self.TASK_PREFIX):
                if not self._monitoring:
                    break

                self._handle_task_event(event)

        except Exception as e:
            self.logger.error(f"Error in task monitoring: {e}")

    def _handle_task_event(self, event) -> None:
        """处理agent任务事件。

        Args:
            event: ETCD watch event
        """
        try:
            if isinstance(event, PutEvent):
                task_data = json.loads(event.value.decode())
                status = task_data.get("status")

                if status == "running":
                    # running状态忽略
                    return
                elif status == "error":
                    # error状态更新对应子网配置参数status为error
                    self._update_subnet_status_on_task_error(task_data)
                elif status == "complete":
                    # complete状态根据任务type处理
                    task_type = task_data.get("type")
                    if task_type == "update":
                        # update任务完成，更新子网配置参数status为activate
                        self._update_subnet_status_on_task_complete(task_data)
                    elif task_type == "delete":
                        # delete任务完成，删除对应子网配置参数
                        self._delete_subnet_on_task_complete(task_data)

        except Exception as e:
            self.logger.error(f"Error handling task event: {e}")

    def _update_subnet_status_on_task_error(self, task_data: Dict[str, Any]) -> None:
        """任务错误时更新子网状态。"""
        shard_id = task_data.get("shard_id")
        vlan_id = task_data.get("vlan_id")
        key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
        data, _ = self.etcd_client.get_json(key)
        if data:
            data["status"] = "error"
            self.etcd_client.put_json(key, data)
          
        pass

    def _update_subnet_status_on_task_complete(self, task_data: Dict[str, Any]) -> None:
        """任务完成时更新子网状态。"""
        shard_id = task_data.get("shard_id")
        vlan_id = task_data.get("vlan_id")
        key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
        data, _ = self.etcd_client.get_json(key)
        if data:
            data["status"] = "activate"
            self.etcd_client.put_json(key, data)
        pass

    def _delete_subnet_on_task_complete(self, task_data: Dict[str, Any]) -> None:
        """删除任务完成时删除子网配置。"""
        shard_id = task_data.get("shard_id")
        vlan_id = task_data.get("vlan_id")
        key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
        self.etcd_client.delete(key)
        pass

import etcd3
import time, threading

# --- 1. 连接到 etcd ---
try:
    # 默认连接到 localhost:2379
    client = etcd3.client(host='*************', port=30379)
    # 检查一下连接是否成功
    client.status()
    print("✅ 成功连接到 etcd！")
except Exception as e:
    print(f"❌ 连接失败: {e}")
    print("👉 请确保你的 etcd 服务正在运行。")
    exit()

# 定义我们要操作的键和值
# MY_KEY = '/network/nodes/node-1'
# INITIAL_VALUE = 'abc-123-def-456'
# UPDATED_VALUE = 'xyz-987-uvw-654'


# --- 2. 写入 (Put) 数据 ---
# print("\n--- 写入操作 ---")
# client.put(MY_KEY, INITIAL_VALUE)
# print(f"🔑 已写入: Key='{MY_KEY}', Value='{INITIAL_VALUE}'")


# --- 3. 读取 (Get) 数据 ---
# print("\n--- 读取操作 ---")
# value, metadata = client.get(MY_KEY)
# # 注意：etcd 返回的值是 bytes 类型, 需要解码成字符串
# if value is not None:
#     print(f"🔍 读取到: Value='{value.decode('utf-8')}'")
#     # metadata 包含很多有用的信息，比如版本号
#     print(f"  (数据版本: {metadata.mod_revision})")


# --- 4. 监视 (Watch) 数据变化 ---
# Watch 是一个持续的过程，我们这里用一个简单的循环来演示
# 在真实应用中，你可能会把它放在一个后台线程里
# print("\n--- 监视操作 ---")
# print(f"👀 正在监视 Key '{MY_KEY}' 的变化... (这是一个演示，10秒后会自动结束)")
# print("✨ 你可以现在打开另一个终端，用下面的命令来修改它，看看会发生什么：")
# print(f"docker exec my-etcd etcdctl put {MY_KEY} {UPDATED_VALUE}")

def listen_to_changes():
    # 监听指定前缀的变化
    events_iterator, cancel = client.watch_prefix("/network/server/register/")
    for event in events_iterator:
        print(f"🔔 监视到事件: {event.event_type} - Key='{event.key.decode('utf-8')}'")
        print(f"💥 新值: '{event.value.decode('utf-8')}'")

def ensure_and_refresh_lease(client, key, value, ttl=15):
    """
    一个智能函数，确保一个 key 关联着一个有效的租约。

    - 如果 key 已有关联的租约，则对其进行一次续期。
    - 如果 key 不存在或没有关联租约，则创建一个新的租约并与该 key 关联。

    :param client: etcd3 客户端实例。
    :param key: 要操作的 key。
    :param value: 当需要创建 key 或更新关联时使用的 value。
    :param ttl: 当需要创建新租约时使用的 TTL (秒)。
    :return: (lease_id, was_created) 一个元组，包含操作的租约ID和是否创建了新租约的布尔值。
    """
    print(f"\n--- 智能续约检查 Key: '{key}' ---")
    
    # 1. 首先，获取 key 的当前状态和元数据
    value_bytes, metadata = client.get(key)

    # 2. 判断 key 是否存在并且已经有关联的租约 (lease_id 不为 0)
    if metadata and metadata.lease_id != 0:
        # --- 路径 A: 租约已存在，进行续期 ---
        existing_lease_id = metadata.lease_id
        print(f"✅ 发现已存在的租约，ID: {existing_lease_id}。准备续期...")
        
        try:
            # 尝试对这个租约进行一次性刷新
            client.lease(existing_lease_id).refresh()
            print(f"✨ 租约 {existing_lease_id} 续期成功！")
            return (existing_lease_id, False)
        except Exception as e:
            # 如果续期失败（比如租约在 get 和 refresh 之间恰好过期了），
            # 我们将按“创建新租约”的逻辑继续处理。
            print(f"⚠️ 续期租约 {existing_lease_id} 失败: {e}。将尝试创建新租约。")

    # --- 路径 B: Key 不存在或没有租约，创建并关联一个新租约 ---
    print(f"ℹ️ Key 不存在或没有关联租约。准备创建新租约...")
    
    # 3. 创建一个新的租约
    new_lease = client.lease(ttl)
    print(f"📝 新租约已创建，ID: {new_lease.id}, TTL: {ttl}s")

    # 4. 将 key 与新租约关联起来并写入 etcd
    client.put(key, value, lease=new_lease)
    print(f"🔗 Key '{key}' 已成功关联到新租约 {new_lease.id}。")
    
    return (new_lease.id, True)


try:
    # 创建一个监视器，设置一个10秒的超时
    # watch_id = client.add_watch_prefix_callback(MY_KEY, lambda event: event_handler(event))
    # 创建新线程进行监听

    t = threading.Thread(target=listen_to_changes, name='ListenThread')
    t.daemon = True  # 设置为守护线程，这样主线程结束时它也会结束
    t.start()
    # event_put()
    #     # cancel()
except Exception:
    print("\n监视超时或被取消。")





# --- 5. 删除 (Delete) 数据 ---
# print("\n--- 删除操作 ---")
# was_deleted = client.delete(MY_KEY)
# if was_deleted:
#     print(f"🗑️ 成功删除 Key='{MY_KEY}'")

# 验证一下是否真的删除了
# value, metadata = client.get(MY_KEY)
# if value is None:
#     print("✅ 验证成功，Key 已不复存在。")


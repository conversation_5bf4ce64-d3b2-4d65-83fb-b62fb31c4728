# Environment Variable Configuration

V-Switch supports environment variable overrides for all configuration settings in `server_config.yaml` and `agent_config.yaml`. This allows for flexible deployment in different environments without modifying configuration files.

## Overview

Environment variables follow the naming pattern: `VSWITCH_<SECTION>_<KEY>`

- All environment variables start with `VSWITCH_`
- Section names are in uppercase (e.g., `ETCD`, `LOGGING`, `SERVER`, `AGENT`)
- Key names are in uppercase with underscores

## Priority Order

Configuration values are applied in the following order (highest to lowest priority):

1. **Environment Variables** (highest priority)
2. **Configuration File Values** (YAML files)
3. **Default Values** (lowest priority)

## Available Environment Variables

### Common Configuration (Both Server and Agent)

#### ETCD Configuration
- `VSWITCH_ETCD_HOST` - ETCD server hostname (default: localhost)
- `VSWITCH_ETCD_PORT` - ETCD server port (default: 2379)
- `VSWITCH_ETCD_TIMEOUT` - Connection timeout in seconds (default: 5.0)
- `VSWITCH_ETCD_USERNAME` - ETCD username (default: empty)
- `VSWITCH_ETCD_PASSWORD` - ETCD password (default: empty)

#### Logging Configuration
- `VSWITCH_LOGGING_LEVEL` - Log level: DEBUG, INFO, WARNING, ERROR (default: INFO)
- `VSWITCH_LOGGING_FILE` - Log file path (default: empty, logs to console)
- `VSWITCH_LOGGING_CONSOLE` - Enable console logging: true/false (default: true)

### Server-Specific Configuration

- `VSWITCH_SERVER_PORT` - Server listening port (default: 30090)
- `VSWITCH_SERVER_SHARD_COUNT` - Number of shards (default: 32)

### Agent-Specific Configuration

- `VSWITCH_AGENT_ID` - Unique agent identifier (default: empty)
- `VSWITCH_AGENT_CHECK_ENV` - Enable environment checking: true/false (default: true)
- `VSWITCH_AGENT_HEARTBEAT_INTERVAL` - Heartbeat interval in seconds (default: 30.0)
- `VSWITCH_AGENT_MAX_RETRIES` - Maximum retry attempts (default: 3)

## Data Types and Conversion

Environment variables are automatically converted to the appropriate data types:

- **String**: Used as-is
- **Integer**: Converted from string to int
- **Float**: Converted from string to float
- **Boolean**: Accepts multiple formats:
  - True: `true`, `1`, `yes`, `on`, `enabled`
  - False: `false`, `0`, `no`, `off`, `disabled`

## Usage Examples

### Development Environment

```bash
# Set up local development environment
export VSWITCH_ETCD_HOST=localhost
export VSWITCH_ETCD_PORT=2379
export VSWITCH_LOGGING_LEVEL=DEBUG
export VSWITCH_SERVER_PORT=8080

# Start server
python -m v_switch.server --config config/server_config.yaml
```

### Production Environment

```bash
# Production environment with remote ETCD cluster
export VSWITCH_ETCD_HOST=etcd-cluster.production.local
export VSWITCH_ETCD_PORT=2379
export VSWITCH_ETCD_USERNAME=vswitch_user
export VSWITCH_ETCD_PASSWORD=secure_password
export VSWITCH_LOGGING_LEVEL=INFO
export VSWITCH_LOGGING_FILE=/var/log/vswitch/server.log
export VSWITCH_SERVER_SHARD_COUNT=128

# Start server
python -m v_switch.server --config config/server_config.yaml
```

### Docker Environment

```bash
docker run -d \
  -e VSWITCH_ETCD_HOST=etcd-service \
  -e VSWITCH_ETCD_PORT=2379 \
  -e VSWITCH_SERVER_PORT=8080 \
  -e VSWITCH_LOGGING_LEVEL=INFO \
  -p 8080:8080 \
  vswitch:latest
```

### Kubernetes Environment

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: vswitch-config
data:
  VSWITCH_ETCD_HOST: "etcd-service.default.svc.cluster.local"
  VSWITCH_ETCD_PORT: "2379"
  VSWITCH_LOGGING_LEVEL: "INFO"
  VSWITCH_SERVER_PORT: "8080"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vswitch-server
spec:
  template:
    spec:
      containers:
      - name: vswitch
        image: vswitch:latest
        envFrom:
        - configMapRef:
            name: vswitch-config
        env:
        - name: VSWITCH_AGENT_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
```

## Utility Scripts

### View Available Environment Variables

```bash
python scripts/show_env_vars.py
```

### Test Environment Variable Override

```bash
python scripts/test_env_override.py
```

### Example Usage Scenarios

```bash
./scripts/example_env_usage.sh
```

## Error Handling

- Invalid environment variable values are ignored with a warning message
- The application continues with the next priority level (config file or default)
- Type conversion errors are logged but don't stop the application

## Best Practices

1. **Use environment variables for deployment-specific settings** like hostnames, ports, and credentials
2. **Keep sensitive information in environment variables** rather than config files
3. **Use consistent naming** following the `VSWITCH_<SECTION>_<KEY>` pattern
4. **Document environment variables** in your deployment scripts
5. **Test configurations** using the provided test scripts before deployment

## Troubleshooting

### Check Current Configuration

You can verify which values are being used by examining the configuration object after initialization:

```python
from v_switch.config.server_config import ServerConfig

config = ServerConfig('config/server_config.yaml')
print(f"ETCD Host: {config.etcd.host}")
print(f"Server Port: {config.server.port}")
print(f"Logging Level: {config.logging.level}")
```

### Debug Environment Variables

Set `VSWITCH_LOGGING_LEVEL=DEBUG` to see which environment variables are being applied:

```bash
VSWITCH_LOGGING_LEVEL=DEBUG python -m v_switch.server
```

This will show messages like:
```
Environment override: VSWITCH_ETCD_HOST=*************
Environment override: VSWITCH_SERVER_PORT=8080
```

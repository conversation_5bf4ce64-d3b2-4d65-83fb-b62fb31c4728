
配置命令样例
```bash
# 创建子网
#创建vlan 的网络空间
ip netns add ns-vlan${VLAN_ID}

# 创建 vlan veth pair
ip link add v-lan-host-${VLAN_ID} type veth peer name v-lan-ns-${VLAN_ID}
# 将veth-ns放入命名空间
ip link set v-lan-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}
# 配置命名空间内网络
ip netns exec ns-vlan${VLAN_ID} ip addr add ${ZW_GA_WAY_IP}/24 dev v-lan-ns-${VLAN_ID}
ip netns exec ns-vlan${VLAN_ID} ip addr

# 添加到vlan网桥并且配置vlanid
ovs-vsctl add-port br-vlan v-lan-host-${VLAN_ID}  tag=${VLAN_ID}
#-------------- 统一启动
ip link set v-lan-host-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set v-lan-ns-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set lo up
ip link set v-lan-host-${VLAN_ID}  up

ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat POSTROUTING ip saddr ${PRE_GA_WAY_IP}.0/24 ip daddr ${PRE_GA_WAY_IP}.0/${ZW_MASK} counter snat to ${ZW_GA_WAY_IP}

# 创建监控网通道
#创建对应的监控 虚拟网卡对
ip link add v-mon-host-${VLAN_ID} type veth peer name v-mon-ns-${VLAN_ID}
ip link set v-mon-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}

ip netns exec ns-vlan${VLAN_ID} ip addr add ${MON_IP}/24 dev v-mon-ns-${VLAN_ID}
#把mon ip配置到监控网桥
ovs-vsctl add-port br-mon v-mon-host-${VLAN_ID}

#启动网卡
ip link set v-mon-host-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set v-mon-ns-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set lo up

#创建链
ip netns exec ns-vlan${VLAN_ID} nft add chain ip nat PREROUTING { type nat hook prerouting priority -100 \; }

#443
ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr *************** tcp dport 443 counter dnat to ${MON_M_IP}:${MON_M_PORT}

ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 30428 counter snat to ************

#放通 69 和 9879 端口
ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 69 counter snat to ************

ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 9879 counter snat to ************

# 创建EIP通道
#创建对应的EIP 虚拟网卡对
ip link add v-eip-host-${VLAN_ID} type veth peer name v-eip-ns-${VLAN_ID}
ip link set v-eip-ns-${VLAN_ID} netns ns-vlan${VLAN_ID}

#把EIP配置到eip 网桥
ovs-vsctl add-port br-eip v-eip-host-${VLAN_ID}

#启动网卡
ip link set v-eip-host-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set v-eip-ns-${VLAN_ID} up
ip netns exec ns-vlan${VLAN_ID} ip link set lo up

ip netns exec ns-vlan${VLAN_ID} ip addr

# 挂载EIP
#创建EIP
ip netns exec ns-vlan${VLAN_ID} ip addr add ${EIP}/24 dev v-eip-ns-${VLAN_ID}

#添加默认网关走EIP
ip netns exec ns-vlan${VLAN_ID} ip route add default via ${EIP_GA_WAY_IP} dev v-eip-ns-${VLAN_ID}

# 添加 nat , 把子网和 EIP进行对应
ip netns exec ns-vlan${VLAN_ID} nft add rule ip nat PREROUTING ip daddr ${EIP} counter dnat to ${ZW_IP}
ip netns exec ns-vlan${VLAN_ID} nft add table ip nat
ip netns exec ns-vlan${VLAN_ID}  nft add rule ip nat POSTROUTING \
    ip saddr ${ZW_IP} \
    ip daddr != { 10.0.0.0/8, **********/12, ***********/24,***********/24 } \
    snat to ${EIP}
    
ip netns exec ns-vlan${VLAN_ID}  nft list table ip nat
```

配置脚本
```bash
#!/bin/bash

# This script is generated based on doc/test.md
# It configures the network for a VLAN.
# Before running, please set the following variables.

# --- Variables ---
# Please fill in these values before running the script
VLAN_ID=""
ZW_GA_WAY_IP=""
PRE_GA_WAY_IP=""
ZW_MASK=""
MON_IP=""
MON_M_IP=""
MON_M_PORT=""
EIP=""
ZW_IP=""
EIP_GA_WAY_IP=""

# Check if VLAN_ID is set
if [ -z "$VLAN_ID" ]; then
    echo "Error: VLAN_ID is not set. Please edit the script and set the variables."
    exit 1
fi

echo "--- Starting network configuration for VLAN_ID: $VLAN_ID ---"

# --- Subnet Creation ---

# Create vlan network namespace if it doesn't exist
if ! ip netns list | grep -q "ns-vlan${VLAN_ID}"; then
    echo "Creating network namespace ns-vlan${VLAN_ID}"
    ip netns add "ns-vlan${VLAN_ID}"
else
    echo "Network namespace ns-vlan${VLAN_ID} already exists."
fi

# Create vlan veth pair if it doesn't exist
if ! ip link show "v-lan-host-${VLAN_ID}" &> /dev/null; then
    echo "Creating veth pair v-lan-host-${VLAN_ID} <--> v-lan-ns-${VLAN_ID}"
    ip link add "v-lan-host-${VLAN_ID}" type veth peer name "v-lan-ns-${VLAN_ID}"
else
    echo "veth pair v-lan-host-${VLAN_ID} already exists."
fi

# Move veth-ns to namespace if it's not already there
if ip link show "v-lan-ns-${VLAN_ID}" &> /dev/null; then
    echo "Moving v-lan-ns-${VLAN_ID} to namespace ns-vlan${VLAN_ID}"
    ip link set "v-lan-ns-${VLAN_ID}" netns "ns-vlan${VLAN_ID}"
else
    echo "v-lan-ns-${VLAN_ID} is not in the main namespace, assuming it's already in ns-vlan${VLAN_ID}."
fi

# Configure network inside namespace
echo "Configuring IP address for v-lan-ns-${VLAN_ID} in namespace."
ip netns exec "ns-vlan${VLAN_ID}" ip addr add "${ZW_GA_WAY_IP}/24" dev "v-lan-ns-${VLAN_ID}"
echo "Current IP addresses in namespace ns-vlan${VLAN_ID}:"
ip netns exec "ns-vlan${VLAN_ID}" ip addr

# Add to vlan bridge if not already added
if ! ovs-vsctl list-ports br-vlan | grep -q "v-lan-host-${VLAN_ID}"; then
    echo "Adding v-lan-host-${VLAN_ID} to br-vlan with tag ${VLAN_ID}"
    ovs-vsctl add-port br-vlan "v-lan-host-${VLAN_ID}" tag="${VLAN_ID}"
else
    echo "Port v-lan-host-${VLAN_ID} already exists in br-vlan."
fi

# --- Bring interfaces up ---
echo "Bringing up interfaces..."
ip link set "v-lan-host-${VLAN_ID}" up
ip netns exec "ns-vlan${VLAN_ID}" ip link set "v-lan-ns-${VLAN_ID}" up
ip netns exec "ns-vlan${VLAN_ID}" ip link set lo up

# --- NAT rule for subnet ---
echo "Adding NAT rule for subnet..."
# Note: This script doesn't check for the existence of nft rules to avoid complexity.
# If you need idempotency, consider flushing the chains before running this.
ip netns exec "ns-vlan${VLAN_ID}" nft add rule ip nat POSTROUTING ip saddr "${PRE_GA_WAY_IP}.0/24" ip daddr "${PRE_GA_WAY_IP}.0/${ZW_MASK}" counter snat to "${ZW_GA_WAY_IP}"

# --- Monitoring Network Channel ---

# Create monitoring veth pair if it doesn't exist
if ! ip link show "v-mon-host-${VLAN_ID}" &> /dev/null; then
    echo "Creating veth pair v-mon-host-${VLAN_ID} <--> v-mon-ns-${VLAN_ID}"
    ip link add "v-mon-host-${VLAN_ID}" type veth peer name "v-mon-ns-${VLAN_ID}"
else
    echo "veth pair v-mon-host-${VLAN_ID} already exists."
fi

# Move veth-ns to namespace
if ip link show "v-mon-ns-${VLAN_ID}" &> /dev/null; then
    echo "Moving v-mon-ns-${VLAN_ID} to namespace ns-vlan${VLAN_ID}"
    ip link set "v-mon-ns-${VLAN_ID}" netns "ns-vlan${VLAN_ID}"
else
    echo "v-mon-ns-${VLAN_ID} is not in the main namespace, assuming it's already in ns-vlan${VLAN_ID}."
fi

echo "Configuring IP address for v-mon-ns-${VLAN_ID} in namespace."
ip netns exec "ns-vlan${VLAN_ID}" ip addr add "${MON_IP}/24" dev "v-mon-ns-${VLAN_ID}"

# Add to mon bridge if not already added
if ! ovs-vsctl list-ports br-mon | grep -q "v-mon-host-${VLAN_ID}"; then
    echo "Adding v-mon-host-${VLAN_ID} to br-mon"
    ovs-vsctl add-port br-mon "v-mon-host-${VLAN_ID}"
else
    echo "Port v-mon-host-${VLAN_ID} already exists in br-mon."
fi

# Bring monitoring interfaces up
echo "Bringing up monitoring interfaces..."
ip link set "v-mon-host-${VLAN_ID}" up
ip netns exec "ns-vlan${VLAN_ID}" ip link set "v-mon-ns-${VLAN_ID}" up

# --- NFT rules for monitoring ---
echo "Adding NFT rules for monitoring..."
# Create table if it doesn't exist
if ! ip netns exec "ns-vlan${VLAN_ID}" nft list tables | grep -q "table ip nat"; then
    ip netns exec "ns-vlan${VLAN_ID}" nft add table ip nat
fi

# Create chain if it doesn't exist
if ! ip netns exec "ns-vlan${VLAN_ID}" nft list chains ip nat | grep -q "PREROUTING"; then
    ip netns exec "ns-vlan${VLAN_ID}" nft add chain ip nat PREROUTING { type nat hook prerouting priority -100 \; }
fi

ip netns exec "ns-vlan${VLAN_ID}" nft add rule ip nat PREROUTING ip daddr *************** tcp dport 443 counter dnat to "${MON_M_IP}:${MON_M_PORT}"
ip netns exec "ns-vlan${VLAN_ID}" nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 30428 counter snat to ************
ip netns exec "ns-vlan${VLAN_ID}" nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 69 counter snat to ************
ip netns exec "ns-vlan${VLAN_ID}" nft add rule ip nat POSTROUTING ip daddr ************* tcp dport 9879 counter snat to ************

# --- EIP Channel ---

# Create EIP veth pair if it doesn't exist
if ! ip link show "v-eip-host-${VLAN_ID}" &> /dev/null; then
    echo "Creating veth pair v-eip-host-${VLAN_ID} <--> v-eip-ns-${VLAN_ID}"
    ip link add "v-eip-host-${VLAN_ID}" type veth peer name "v-eip-ns-${VLAN_ID}"
else
    echo "veth pair v-eip-host-${VLAN_ID} already exists."
fi

# Move veth-ns to namespace
if ip link show "v-eip-ns-${VLAN_ID}" &> /dev/null; then
    echo "Moving v-eip-ns-${VLAN_ID} to namespace ns-vlan${VLAN_ID}"
    ip link set "v-eip-ns-${VLAN_ID}" netns "ns-vlan${VLAN_ID}"
else
    echo "v-eip-ns-${VLAN_ID} is not in the main namespace, assuming it's already in ns-vlan${VLAN_ID}."
fi

# Add to eip bridge if not already added
if ! ovs-vsctl list-ports br-eip | grep -q "v-eip-host-${VLAN_ID}"; then
    echo "Adding v-eip-host-${VLAN_ID} to br-eip"
    ovs-vsctl add-port br-eip "v-eip-host-${VLAN_ID}"
else
    echo "Port v-eip-host-${VLAN_ID} already exists in br-eip."
fi

# Bring EIP interfaces up
echo "Bringing up EIP interfaces..."
ip link set "v-eip-host-${VLAN_ID}" up
ip netns exec "ns-vlan${VLAN_ID}" ip link set "v-eip-ns-${VLAN_ID}" up

echo "Current IP addresses in namespace ns-vlan${VLAN_ID}:"
ip netns exec "ns-vlan${VLAN_ID}" ip addr

# --- Mount EIP ---
echo "Mounting EIP ${EIP}..."
ip netns exec "ns-vlan${VLAN_ID}" ip addr add "${EIP}/24" dev "v-eip-ns-${VLAN_ID}"
ip netns exec "ns-vlan${VLAN_ID}" ip route add default via "${EIP_GA_WAY_IP}" dev "v-eip-ns-${VLAN_ID}"

# --- NFT rules for EIP ---
echo "Adding NFT rules for EIP..."
ip netns exec "ns-vlan${VLAN_ID}" nft add rule ip nat PREROUTING ip daddr "${EIP}" counter dnat to "${ZW_IP}"
ip netns exec "ns-vlan${VLAN_ID}" nft add rule ip nat POSTROUTING ip saddr "${ZW_IP}" ip daddr != { 10.0.0.0/8, **********/12, ***********/24, ***********/24 } snat to "${EIP}"

echo "Final nftables ruleset for table 'nat':"
ip netns exec "ns-vlan${VLAN_ID}" nft list table ip nat

echo "--- Configuration script for VLAN_ID: $VLAN_ID finished. ---"

```
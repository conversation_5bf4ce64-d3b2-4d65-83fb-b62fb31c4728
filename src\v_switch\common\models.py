"""
Data models for v-switch system.
"""

from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json


class SubnetStatus(Enum):
    """子网状态枚举。"""
    ACTIVATE = "activate"
    ERROR = "error"
    UPDATING = "updating"
    DELETION = "deletion"


class TaskStatus(Enum):
    """任务状态枚举。"""
    RUNNING = "running"
    COMPLETE = "complete"
    ERROR = "error"


class TaskType(Enum):
    """任务类型枚举。"""
    UPDATE = "update"
    DELETE = "delete"


@dataclass
class SubnetConfig:
    """子网配置部分。"""
    gateway_ip: str
    pre_ip: str


@dataclass
class MonnetConfig:
    """监控网络配置部分。"""
    mon_ip: str
    mon_m_ip: str
    mon_m_ports: int
    ext_m_ports: List[int]


@dataclass
class EipMountConfig:
    """EIP挂载配置。"""
    eip: str
    internal_ip: str


@dataclass
class EipnetConfig:
    """EIP配置部分。"""
    gateway_ip: str
    mounts: List[EipMountConfig]


@dataclass
class SubnetConfigParams:
    """子网配置参数完整结构。"""
    id: str
    version: int
    tenant_id: str
    shard_id: str
    vlan_id: str
    status: SubnetStatus
    subnet: Optional[SubnetConfig] = None
    monnet: Optional[MonnetConfig] = None
    eipnet: Optional[EipnetConfig] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        result = {
            "id": self.id,
            "version": self.version,
            "tenant_id": self.tenant_id,
            "shard_id": self.shard_id,
            "vlan_id": self.vlan_id,
            "status": self.status.value,
        }

        if self.subnet:
            result["subnet"] = {
                "gateway_ip": self.subnet.gateway_ip,
                "pre_ip": self.subnet.pre_ip,
            }

        if self.monnet:
            result["monnet"] = {
                "mon_ip": self.monnet.mon_ip,
                "mon_m_ip": self.monnet.mon_m_ip,
                "mon_m_ports": self.monnet.mon_m_ports,
                "ext_m_ports": self.monnet.ext_m_ports,
            }

        if self.eipnet:
            result["eipnet"] = {
                "gateway_ip": self.eipnet.gateway_ip,
                "mounts": [
                    {
                        "eip": mount.eip,
                        "internal_ip": mount.internal_ip,
                    }
                    for mount in self.eipnet.mounts
                ]
            }

        return result

    def hash_eip(self) -> bool:
        """判断是否挂载了EIP。"""
        return self.eipnet is not None and len(self.eipnet.mounts) > 0

    @classmethod
    def from_file(cls, file_path: str) -> 'SubnetConfigParams':
        """从文件加载配置
        
        Args:
            file_path (str): 配置文件路径

        """
        with open(file_path, 'r', encoding='utf-8') as file:
          # 使用 json.load() 从文件中读取并解析JSON数据
          data_dict = json.load(file)
        return SubnetConfigParams.from_dict(data_dict)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SubnetConfigParams':
        """从字典创建实例。"""
        subnet = None
        if "subnet" in data and data["subnet"]:
            subnet = SubnetConfig(
                gateway_ip=data["subnet"]["gateway_ip"],
                pre_ip=data["subnet"]["pre_ip"]
            )

        monnet = None
        if "monnet" in data and data["monnet"]:
            monnet = MonnetConfig(
                mon_ip=data["monnet"]["mon_ip"],
                mon_m_ip=data["monnet"]["mon_m_ip"],
                mon_m_ports=data["monnet"]["mon_m_ports"],
                ext_m_ports=data["monnet"]["ext_m_ports"]
            )

        eipnet = None
        if "eipnet" in data and data["eipnet"]:
            eipnet_data = data["eipnet"]
            mounts_list = []
            if "mounts" in eipnet_data and eipnet_data["mounts"]:
                mounts_list = [
                    EipMountConfig(
                        eip=mount["eip"],
                        internal_ip=mount["internal_ip"]
                    )
                    for mount in eipnet_data["mounts"]
                ]
            eipnet = EipnetConfig(
                gateway_ip=eipnet_data["gateway_ip"],
                mounts=mounts_list
            )

        return cls(
            id=data["id"],
            version=data["version"],
            tenant_id=data["tenant_id"],
            shard_id=data["shard_id"],
            vlan_id=data["vlan_id"],
            status=SubnetStatus(data["status"]),
            subnet=subnet,
            monnet=monnet,
            eipnet=eipnet
        )


@dataclass
class TaskData:
    """任务数据结构。"""
    task_id: str
    shard_id: str
    vlan_id: str
    type: TaskType
    version: int
    status: TaskStatus
    update_config: bool
    cmds: List[str] = field(default_factory=list)
    plan: int = 0
    message: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "task_id": self.task_id,
            "shard_id": self.shard_id,
            "vlan_id": self.vlan_id,
            "type": self.type.value,
            "version": self.version,
            "status": self.status.value,
            "update_config": self.update_config,
            "cmds": self.cmds,
            "plan": self.plan,
            "message": self.message,
        }

    def append_cmd(self, cmd: str):
        if cmd:
            return
        if self.cmds:
            self.cmds.append(cmd)
            self.plan += 1

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskData':
        """从字典创建实例。"""
        return cls(
            task_id=data["task_id"],
            shard_id=data["shard_id"],
            vlan_id=data["vlan_id"],
            type=TaskType(data["type"]),
            version=data["version"],
            status=TaskStatus(data["status"]),
            update_config=data["update_config"],
            cmds=data.get("cmds", []),
            plan=data.get("plan", 0),
            message=data.get("message", "")
        )


@dataclass
class ServerInfo:
    """服务器信息结构。"""
    shard_count: int

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "shard_count": self.shard_count,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ServerInfo':
        """从字典创建实例。"""
        return cls(
            shard_count=data["shard_count"],
        )


@dataclass
class AgentInfo:
    """Agent信息结构。"""
    online: bool
    agent_id: str
    offline_time: int
    shard: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "online": self.online,
            "agent_id": self.agent_id,
            "offline_time": self.offline_time,
            "shard": self.shard,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentInfo':
        """从字典创建实例。"""
        return cls(
            online=data["online"],
            agent_id=data["agent_id"],
            offline_time=data["offline_time"],
            shard=data.get("shard", [])
        )


@dataclass
class ServerMetadata:
    """服务器元数据结构。"""
    server: ServerInfo
    agent: List[AgentInfo] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "server": self.server.to_dict(),
            "agent": [agent.to_dict() for agent in self.agent],
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ServerMetadata':
        """从字典创建实例。"""
        server = ServerInfo.from_dict(data["server"])
        agents = [AgentInfo.from_dict(agent_data) for agent_data in data.get("agent", [])]
        return cls(
            server=server,
            agent=agents
        )


@dataclass
class AgentRegisterData:
    """Agent注册数据结构。"""
    status: str  # "running" | "error"
    agent_id: str
    last_heartbeat_time: int

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "status": self.status,
            "agent_id": self.agent_id,
            "lase_heartbeat_time": self.last_heartbeat_time,  # 保持原设计中的拼写错误
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentRegisterData':
        """从字典创建实例。"""
        return cls(
            status=data["status"],
            agent_id=data["agent_id"],
            last_heartbeat_time=data.get("lase_heartbeat_time", 0)  # 保持原设计中的拼写错误
        )


# 本地配置相关的数据模型

@dataclass
class VethPairAddress:
    """Veth pair地址配置。"""
    ip: str
    prefix: int


@dataclass
class VethPairConfig:
    """Veth pair配置。"""
    name: str
    peer_name: str
    peer_namespace: str
    peer_address: List[VethPairAddress] = field(default_factory=list)
    state: str = "up"


@dataclass
class OvsPortConfig:
    """OVS端口配置。"""
    bridge: str
    port: str
    tag: Optional[int] = None
    namespace: Optional[str] = None
    type: Optional[str] = None
    address: List[VethPairAddress] = field(default_factory=list)
    state: str = "up"


@dataclass
class RouteConfig:
    """路由配置。"""
    namespace: str
    destination: str
    gateway: str
    device: str


@dataclass
class NftMatch:
    """NFT匹配条件。"""
    ip: Optional[Dict[str, str]] = None
    tcp: Optional[Dict[str, Union[str, int]]] = None


@dataclass
class NftAction:
    """NFT动作。"""
    dnat: Optional[Dict[str, str]] = None
    snat: Optional[Dict[str, str]] = None


@dataclass
class NftRule:
    """NFT规则。"""
    match: NftMatch
    action: NftAction
    counter: bool = True


@dataclass
class NftChain:
    """NFT链。"""
    name: str
    type: str
    hook: str
    priority: int
    rules: List[NftRule] = field(default_factory=list)


@dataclass
class NftTable:
    """NFT表。"""
    family: str
    name: str
    chains: List[NftChain] = field(default_factory=list)


@dataclass
class LocalSubnetConfig:
    """本地子网配置完整结构。"""
    version: int
    tenant_id: str
    vlan_id: str
    namespaces: List[str] = field(default_factory=list)
    veth_pairs: List[VethPairConfig] = field(default_factory=list)
    ovs_ports: List[OvsPortConfig] = field(default_factory=list)
    routes: List[RouteConfig] = field(default_factory=list)
    nft_tables: List[NftTable] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式。"""
        return {
            "version": self.version,
            "tenant_id": self.tenant_id,
            "vlan_id": self.vlan_id,
            "namespaces": self.namespaces,
            "veth_pairs": [
                {
                    "name": vp.name,
                    "peer_name": vp.peer_name,
                    "peer_namespace": vp.peer_namespace,
                    "peer_address": [{"ip": addr.ip, "prefix": addr.prefix} for addr in vp.peer_address],
                    "state": vp.state,
                }
                for vp in self.veth_pairs
            ],
            "ovs_ports": [
                {
                    k: v for k, v in {
                        "bridge": port.bridge,
                        "port": port.port,
                        "tag": port.tag,
                        "namespace": port.namespace,
                        "type": port.type,
                        "address": [{"ip": addr.ip, "prefix": addr.prefix} for addr in port.address] if port.address else None,
                        "state": port.state,
                    }.items() if v is not None
                }
                for port in self.ovs_ports
            ],
            "routes": [
                {
                    "namespace": route.namespace,
                    "destination": route.destination,
                    "gateway": route.gateway,
                    "device": route.device,
                }
                for route in self.routes
            ],
            "nft_tables": [
                {
                    "family": table.family,
                    "name": table.name,
                    "chains": [
                        {
                            "name": chain.name,
                            "type": chain.type,
                            "hook": chain.hook,
                            "priority": chain.priority,
                            "rules": [
                                {
                                    "match": {
                                        k: v for k, v in {
                                            "ip": rule.match.ip,
                                            "tcp": rule.match.tcp,
                                        }.items() if v is not None
                                    },
                                    "action": {
                                        k: v for k, v in {
                                            "dnat": rule.action.dnat,
                                            "snat": rule.action.snat,
                                        }.items() if v is not None
                                    },
                                    "counter": rule.counter,
                                }
                                for rule in chain.rules
                            ],
                        }
                        for chain in table.chains
                    ],
                }
                for table in self.nft_tables
            ],
        }

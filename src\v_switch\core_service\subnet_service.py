"""
Subnet service for managing subnet configurations.
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from v_switch.common.etcd_client import ETCDClient
from v_switch.config.server_config import ServerConfig
from v_switch.common.models import SubnetConfigParams, SubnetConfig, MonnetConfig, EipMountConfig, EipnetConfig, SubnetStatus
from v_switch.core_service.agent_manager import AgentManager
from v_switch.core_service.shard_manager import ShardManager
from v_switch.core_service.ip_allocator import IPAllocator


class SubnetService:
    """子网管理服务。"""

    def __init__(self, etcd_client: ETCDClient, config: ServerConfig, shard_manager: ShardManager, mon_ip_allocator: IPAllocator, agent_manager: AgentManager):
        """初始化子网服务。

        Args:
            etcd_client: ETCD client instance
            config: Server configuration
            shard_manager: Shard manager instance
            mon_ip_allocator: IPAllocator for monnet
            agent_manager: Agent manager instance for status checking
        """
        self.etcd_client = etcd_client
        self.config = config
        self.shard_manager = shard_manager
        self.mon_ip_allocator = mon_ip_allocator
        self.agent_manager = agent_manager
        self.logger = logging.getLogger(__name__)

    def create_subnet(self, tenant_id: str, vlan_id: int, subnet_gw_ip: str) -> Optional[str]:
        """创建子网。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            subnet_gw_ip: Subnet gateway IP

        Returns:
            Subnet ID if successful, None otherwise
        """
        try:
            # 0. 检查agent状态
            if not self.agent_manager.check_agent_status():
                self.logger.error("Agent status check failed, cannot create subnet")
                return None

            # 1. 检查vlan_id是否在允许的范围内
            if not (self.config.subnet.vlan_min <= vlan_id <= self.config.subnet.vlan_max):
                raise ValueError(f"VLAN ID {vlan_id} is out of range ({self.config.subnet.vlan_min}-{self.config.subnet.vlan_max})")
            # 获取分片ID
            # shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            shard_id = self.shard_manager.get_shard_for_tenant(f"vlan-{vlan_id}")

            # 2. 检查etcd中key是否存在
            subnet_key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
            existing_config, _ = self.etcd_client.get_json(subnet_key)
            if existing_config:
                raise ValueError(f"Subnet for VLAN {vlan_id} already exists")

            # 3. 创建子网配置参数
            subnet_id = str(uuid.uuid4())

            # 4. 配置参数中添加subnet部分
            # 从subnet_gw_ip提取pre_ip (例如: *********** -> 10.50.101)
            ip_parts = subnet_gw_ip.split('.')
            if len(ip_parts) != 4:
                raise ValueError(f"Invalid subnet gateway IP: {subnet_gw_ip}")
            pre_ip = '.'.join(ip_parts[:3])

            subnet_config = SubnetConfig(
                gateway_ip=subnet_gw_ip,
                pre_ip=pre_ip
            )

            # 5. 配置参数中添加monnet部分，mon_ip从mon_ip_allocator分配
            mon_ip = self.mon_ip_allocator.allocate_ip()
            if not mon_ip:
                self.logger.error("Failed to allocate monitoring IP")
                return None

            monnet_config = MonnetConfig(
                mon_ip=mon_ip,
                mon_m_ip=self.config.monnet.host,
                mon_m_ports=self.config.monnet.port,
                ext_m_ports=self.config.monnet.ext_ports.copy()
            )

            # 创建完整的子网配置参数
            subnet_params = SubnetConfigParams(
                id=subnet_id,
                version=1,
                tenant_id=tenant_id,
                shard_id=str(shard_id),
                vlan_id=str(vlan_id),
                status=SubnetStatus.UPDATING,
                subnet=subnet_config,
                monnet=monnet_config,
                eipnet=None
            )

            # 8. 将子网配置参数写入etcd
            success = self.etcd_client.put_json(subnet_key, subnet_params.to_dict())
            if success:
                self.logger.info(f"Created subnet {subnet_id} for VLAN {vlan_id}")
                return subnet_id

        except ValueError as e:
            raise e
        except Exception as e:
            self.logger.error(f"Error creating subnet: {e}")
        # 如果写入失败，释放分配的IP
        if mon_ip:
            self.logger.warning(f"Failed to create subnet for VLAN {vlan_id}, releasing IP {mon_ip}")
            self.mon_ip_allocator.release_ip(mon_ip)
            self.logger.error(f"Failed to create subnet for VLAN {vlan_id}")
        return None

    def query_subnet(self, tenant_id: str, vlan_id: int) -> Optional[Dict[str, Any]]:
        """查询子网。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier

        Returns:
            Subnet configuration or None if not found
        """
        try:
            # 获取分片ID
            # shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            shard_id = self.shard_manager.get_shard_for_tenant(f"vlan-{vlan_id}")

            # 从etcd查询子网配置参数
            subnet_key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
            config_data, _ = self.etcd_client.get_json(subnet_key)

            if not config_data:
                self.logger.error(f"Subnet for VLAN {vlan_id} not found")
                return None

            return config_data

        except Exception as e:
            self.logger.error(f"Error querying subnet: {e}")
            return None

    def delete_subnet(self, tenant_id: str, vlan_id: int) -> bool:
        """删除子网。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier

        Returns:
            True if successful
        """
        try:
            # 0. 检查agent状态
            if not self.agent_manager.check_agent_status():
                self.logger.error("Agent status check failed, cannot delete subnet")
                return False
            # 获取分片ID
            # shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            shard_id = self.shard_manager.get_shard_for_tenant(f"vlan-{vlan_id}")

            # 从etcd查询子网配置参数
            subnet_key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
            config_data, _ = self.etcd_client.get_json(subnet_key)

            if not config_data:
                self.logger.error(f"Subnet for VLAN {vlan_id} not found")
                return False

            # 解析配置参数
            subnet_params = SubnetConfigParams.from_dict(config_data)

            # 1. 检查子网配置参数status是否为deletion
            if subnet_params and subnet_params.status == SubnetStatus.DELETION:
                self.logger.warning(f"Cannot delete subnet for VLAN {vlan_id}: it is deletion")
                return True

            # 2. 检查eipnet部分是否为空
            if subnet_params.eipnet and subnet_params.eipnet.mounts and len(subnet_params.eipnet.mounts) > 0:
                raise ValueError(f"Cannot delete subnet for VLAN {vlan_id}: EIP mounts still exist")

            # 3. 设置subnet, monnet为null
            # 释放分配的监控IP
            if subnet_params.monnet:
                self.mon_ip_allocator.release_ip(subnet_params.monnet.mon_ip)

            subnet_params.subnet = None
            subnet_params.monnet = None

            # 4. 修改配置参数version + 1
            subnet_params.version += 1

            # 5. 设置配置参数status为deletion
            subnet_params.status = SubnetStatus.DELETION

            # 6. 将更新后的配置数据写入etcd
            success = self.etcd_client.put_json(subnet_key, subnet_params.to_dict())
            if success:
                self.logger.info(f"Marked subnet for VLAN {vlan_id} for deletion")
                return True
            else:
                self.logger.error(f"Failed to delete subnet for VLAN {vlan_id}")
                return False
        except ValueError as e:
            raise e
        except Exception as e:
            self.logger.error(f"Error deleting subnet: {e}")
            return False

    def mount_eip(self, tenant_id: str, vlan_id: int, eip: str, internal_ip: str, eip_gateway: Optional[str] = None) -> bool:
        """挂载EIP。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address
            eip_gateway: EIP gateway (optional, defaults to config.eip.gateway)

        Returns:
            True if successful
        """
        try:
            # 0. 检查agent状态
            if not self.agent_manager.check_agent_status():
                self.logger.error("Agent status check failed, cannot mount EIP")
                return False
            # 获取分片ID
            # shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            shard_id = self.shard_manager.get_shard_for_tenant(f"vlan-{vlan_id}")

            # 1. 从etcd查询子网配置参数
            subnet_key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
            config_data, _ = self.etcd_client.get_json(subnet_key)

            if not config_data:
                self.logger.error(f"Subnet for VLAN {vlan_id} not found")
                return False

            # 解析配置参数
            subnet_params = SubnetConfigParams.from_dict(config_data)

            # 2. 添加eipnet部分
            if eip_gateway is None:
                eip_gateway = self.config.eip.gateway

            new_mount = EipMountConfig(
                eip=eip,
                internal_ip=internal_ip
            )

            # 如果没有eipnet配置，创建新的
            if not subnet_params.eipnet:
                subnet_params.eipnet = EipnetConfig(
                    gateway_ip=eip_gateway,
                    mounts=[new_mount]
                )
            else:
                # 检查是否已经存在相同的EIP挂载
                for existing_mount in subnet_params.eipnet.mounts:
                    if existing_mount.eip == eip:
                        self.logger.warning(f"EIP {eip} already mounted to {existing_mount.internal_ip}")
                        return True

                # 添加新的挂载
                subnet_params.eipnet.mounts.append(new_mount)

            # 3. 修改配置参数version + 1
            subnet_params.version += 1

            # 4. 设置配置参数status为updating
            subnet_params.status = SubnetStatus.UPDATING

            # 5. 将更新后的配置数据写入etcd
            success = self.etcd_client.put_json(subnet_key, subnet_params.to_dict())
            if success:
                self.logger.info(f"Mounted EIP {eip} to {internal_ip} for VLAN {vlan_id}")
                return True
            else:
                self.logger.error(f"Failed to mount EIP {eip} for VLAN {vlan_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error mounting EIP: {e}")
            return False

    def unmount_eip(self, tenant_id: str, vlan_id: int, eip: str, internal_ip: str) -> bool:
        """卸载EIP。

        Args:
            tenant_id: Tenant identifier
            vlan_id: VLAN identifier
            eip: External IP address
            internal_ip: Internal IP address

        Returns:
            True if successful
        """
        try:
            # 0. 检查agent状态
            if not self.agent_manager.check_agent_status():
                self.logger.error("Agent status check failed, cannot unmount EIP")
                return False
            # 获取分片ID
            # shard_id = self.shard_manager.get_shard_for_tenant(tenant_id)
            shard_id = self.shard_manager.get_shard_for_tenant(f"vlan-{vlan_id}")

            # 1. 从etcd查询子网配置参数
            subnet_key = f"/network/server/subnet/{shard_id}/vlan-{vlan_id}"
            config_data, _ = self.etcd_client.get_json(subnet_key)

            if not config_data:
                self.logger.error(f"Subnet for VLAN {vlan_id} not found")
                return False

            # 解析配置参数
            subnet_params = SubnetConfigParams.from_dict(config_data)

            # 2. 检查eipnet部分是否为空
            if not subnet_params.eipnet or not subnet_params.eipnet.mounts:
                self.logger.info(f"No EIP mounts found for VLAN {vlan_id}, nothing to unmount")
                return True

            # 3. 轮询比对eipnet.mounts部分，找到对应的eip，删除对象
            original_count = len(subnet_params.eipnet.mounts)
            subnet_params.eipnet.mounts = [
                mount for mount in subnet_params.eipnet.mounts
                if not (mount.eip == eip)
            ]

            if len(subnet_params.eipnet.mounts) == original_count:
                self.logger.warning(f"EIP mount {eip} -> {internal_ip} not found for VLAN {vlan_id}")
                return True

            # 如果没有剩余的挂载，清空eipnet
            if len(subnet_params.eipnet.mounts) == 0:
                subnet_params.eipnet = None

            # 4. 修改配置参数version + 1
            subnet_params.version += 1

            # 5. 设置配置参数status为updating
            subnet_params.status = SubnetStatus.UPDATING

            # 6. 将更新后的配置数据写入etcd
            success = self.etcd_client.put_json(subnet_key, subnet_params.to_dict())
            if success:
                self.logger.info(f"Unmounted EIP {eip} from {internal_ip} for VLAN {vlan_id}")
                return True
            else:
                self.logger.error(f"Failed to unmount EIP {eip} for VLAN {vlan_id}")
                return False

        except Exception as e:
            self.logger.error(f"Error unmounting EIP: {e}")
            return False

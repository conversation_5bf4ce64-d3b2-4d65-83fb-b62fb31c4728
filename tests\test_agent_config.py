"""
Tests for the agent configuration module.
"""

import pytest
import json
import tempfile
import os
from pathlib import Path

# Import the agent configuration modules
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from agent.config import <PERSON><PERSON>onfig, Config<PERSON>anager, load_config, get_default_config


class TestAgentConfig:
    """Test the AgentConfig class."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = AgentConfig()
        
        assert config.etcd_host == 'localhost'
        assert config.etcd_port == 2379
        assert config.etcd_timeout == 5.0
        assert config.heartbeat_interval == 30.0
        assert config.heartbeat_timeout == 90.0
        assert config.log_level == 'INFO'
        assert config.node_id == 1
        assert config.max_retries == 3
        assert config.enable_ssl == False
    
    def test_config_with_kwargs(self):
        """Test configuration with keyword arguments."""
        config = AgentConfig(
            etcd_host='test-host',
            etcd_port=12345,
            heartbeat_interval=60.0
        )
        
        assert config.etcd_host == 'test-host'
        assert config.etcd_port == 12345
        assert config.heartbeat_interval == 60.0
        # Other values should remain default
        assert config.etcd_timeout == 5.0


class TestConfigManager:
    """Test the ConfigManager class."""
    
    def test_config_manager_init(self):
        """Test ConfigManager initialization."""
        manager = ConfigManager()
        assert manager.config is not None
        assert isinstance(manager.config, AgentConfig)
    
    def test_load_json_config(self):
        """Test loading JSON configuration."""
        config_data = {
            'etcd_host': 'json-host',
            'etcd_port': 9999,
            'heartbeat_interval': 45.0,
            'log_level': 'DEBUG'
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_file = f.name
        
        try:
            manager = ConfigManager()
            config = manager.load_config(config_file)
            
            assert config.etcd_host == 'json-host'
            assert config.etcd_port == 9999
            assert config.heartbeat_interval == 45.0
            assert config.log_level == 'DEBUG'
        finally:
            os.unlink(config_file)
    
    def test_load_python_config(self):
        """Test loading Python configuration."""
        config_content = """
c = get_config()

c.AgentConfig.etcd_host = 'python-host'
c.AgentConfig.etcd_port = 8888
c.AgentConfig.heartbeat_interval = 25.0
c.AgentConfig.log_level = 'WARNING'
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(config_content)
            config_file = f.name
        
        try:
            manager = ConfigManager()
            config = manager.load_config(config_file)
            
            assert config.etcd_host == 'python-host'
            assert config.etcd_port == 8888
            assert config.heartbeat_interval == 25.0
            assert config.log_level == 'WARNING'
        finally:
            os.unlink(config_file)
    
    def test_environment_variables(self):
        """Test loading configuration from environment variables."""
        # Set environment variables
        env_vars = {
            'VSWITCH_AGENT_ETCD_HOST': 'env-host',
            'VSWITCH_AGENT_ETCD_PORT': '7777',
            'VSWITCH_AGENT_HEARTBEAT_INTERVAL': '15.5',
            'VSWITCH_AGENT_LOG_LEVEL': 'ERROR',
            'VSWITCH_AGENT_ENABLE_SSL': 'true'
        }
        
        # Save original environment
        original_env = {}
        for key in env_vars:
            original_env[key] = os.environ.get(key)
        
        try:
            # Set test environment variables
            for key, value in env_vars.items():
                os.environ[key] = value
            
            manager = ConfigManager()
            config = manager.load_config()
            
            assert config.etcd_host == 'env-host'
            assert config.etcd_port == 7777
            assert config.heartbeat_interval == 15.5
            assert config.log_level == 'ERROR'
            assert config.enable_ssl == True
            
        finally:
            # Restore original environment
            for key, value in original_env.items():
                if value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = value
    
    def test_config_validation(self):
        """Test configuration validation."""
        manager = ConfigManager()
        
        # Test invalid port
        manager.config.etcd_port = 99999
        with pytest.raises(ValueError, match="Invalid ETCD port"):
            manager._validate_config()
        
        # Test invalid timeout
        manager.config.etcd_port = 2379  # Reset to valid
        manager.config.etcd_timeout = -1.0
        with pytest.raises(ValueError, match="ETCD timeout must be positive"):
            manager._validate_config()
        
        # Test invalid heartbeat configuration
        manager.config.etcd_timeout = 5.0  # Reset to valid
        manager.config.heartbeat_timeout = 10.0
        manager.config.heartbeat_interval = 20.0
        with pytest.raises(ValueError, match="Heartbeat timeout.*must be greater than"):
            manager._validate_config()
        
        # Test invalid log level
        manager.config.heartbeat_timeout = 90.0  # Reset to valid
        manager.config.heartbeat_interval = 30.0  # Reset to valid
        manager.config.log_level = 'INVALID'
        with pytest.raises(ValueError, match="Invalid log level"):
            manager._validate_config()
    
    def test_save_json_config(self):
        """Test saving configuration as JSON."""
        manager = ConfigManager()
        manager.config.etcd_host = 'save-test-host'
        manager.config.etcd_port = 5555
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config_file = f.name
        
        try:
            manager.save_config(config_file, 'json')
            
            # Load and verify
            with open(config_file, 'r') as f:
                saved_data = json.load(f)
            
            assert saved_data['etcd_host'] == 'save-test-host'
            assert saved_data['etcd_port'] == 5555
            
        finally:
            os.unlink(config_file)
    
    def test_save_python_config(self):
        """Test saving configuration as Python."""
        manager = ConfigManager()
        manager.config.etcd_host = 'save-python-host'
        manager.config.etcd_port = 6666
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            config_file = f.name
        
        try:
            manager.save_config(config_file, 'python')
            
            # Read and verify content
            with open(config_file, 'r') as f:
                content = f.read()
            
            assert 'c.AgentConfig.etcd_host = \'save-python-host\'' in content
            assert 'c.AgentConfig.etcd_port = 6666' in content
            
        finally:
            os.unlink(config_file)


class TestConvenienceFunctions:
    """Test convenience functions."""
    
    def test_get_default_config(self):
        """Test get_default_config function."""
        config = get_default_config()
        assert isinstance(config, AgentConfig)
        assert config.etcd_host == 'localhost'
        assert config.etcd_port == 2379
    
    def test_load_config_function(self):
        """Test load_config function."""
        config_data = {
            'etcd_host': 'function-test-host',
            'etcd_port': 3333
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            config_file = f.name
        
        try:
            config = load_config(config_file)
            assert config.etcd_host == 'function-test-host'
            assert config.etcd_port == 3333
        finally:
            os.unlink(config_file)


if __name__ == '__main__':
    pytest.main([__file__])

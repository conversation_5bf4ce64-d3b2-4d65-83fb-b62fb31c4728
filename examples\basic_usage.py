#!/usr/bin/env python3
"""
Basic usage example for V-Switch Agent configuration.

This example demonstrates how to use the configuration system
and agent functionality.
"""

import sys
import time
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from agent import (
    AgentConfig, 
    ConfigManager, 
    VSwitchAgent,
    load_config,
    get_default_config,
    create_sample_config
)


def example_1_basic_config():
    """Example 1: Basic configuration usage."""
    print("=== Example 1: Basic Configuration ===")
    
    # Get default configuration
    config = get_default_config()
    print(f"Default ETCD host: {config.etcd_host}")
    print(f"Default ETCD port: {config.etcd_port}")
    print(f"Default heartbeat interval: {config.heartbeat_interval}")
    
    # Modify configuration
    config.etcd_host = '*************'
    config.etcd_port = 30379
    config.heartbeat_interval = 60.0
    config.log_level = 'DEBUG'
    
    print(f"Modified ETCD host: {config.etcd_host}")
    print(f"Modified ETCD port: {config.etcd_port}")
    print()


def example_2_config_manager():
    """Example 2: Using ConfigManager."""
    print("=== Example 2: Configuration Manager ===")
    
    # Create configuration manager
    manager = ConfigManager()
    
    # Load configuration (will use defaults if no file found)
    config = manager.load_config()
    
    # Display current configuration
    print("Current configuration:")
    manager.print_config()
    
    # Save sample configuration files
    try:
        create_sample_config('sample_config.json', 'json')
        print("Created sample_config.json")
        
        create_sample_config('sample_config.py', 'python')
        print("Created sample_config.py")
    except Exception as e:
        print(f"Error creating sample configs: {e}")
    
    print()


def example_3_load_from_file():
    """Example 3: Load configuration from file."""
    print("=== Example 3: Load from File ===")
    
    # Create a test configuration file
    test_config = {
        "etcd_host": "test-etcd-server",
        "etcd_port": 12345,
        "heartbeat_interval": 45.0,
        "log_level": "WARNING",
        "node_id": 5
    }
    
    import json
    with open('test_config.json', 'w') as f:
        json.dump(test_config, f, indent=2)
    
    try:
        # Load configuration from file
        config = load_config('test_config.json')
        
        print(f"Loaded ETCD host: {config.etcd_host}")
        print(f"Loaded ETCD port: {config.etcd_port}")
        print(f"Loaded heartbeat interval: {config.heartbeat_interval}")
        print(f"Loaded log level: {config.log_level}")
        print(f"Loaded node ID: {config.node_id}")
        
    except Exception as e:
        print(f"Error loading config: {e}")
    finally:
        # Clean up
        try:
            Path('test_config.json').unlink()
        except:
            pass
    
    print()


def example_4_environment_variables():
    """Example 4: Environment variable configuration."""
    print("=== Example 4: Environment Variables ===")
    
    import os
    
    # Set some environment variables
    os.environ['VSWITCH_AGENT_ETCD_HOST'] = 'env-etcd-server'
    os.environ['VSWITCH_AGENT_ETCD_PORT'] = '9999'
    os.environ['VSWITCH_AGENT_HEARTBEAT_INTERVAL'] = '60.0'  # Must be less than timeout (90.0)
    os.environ['VSWITCH_AGENT_HEARTBEAT_TIMEOUT'] = '180.0'  # Set timeout to be greater than interval
    os.environ['VSWITCH_AGENT_LOG_LEVEL'] = 'ERROR'
    
    try:
        # Load configuration (will pick up environment variables)
        config = load_config()
        
        print(f"ETCD host from env: {config.etcd_host}")
        print(f"ETCD port from env: {config.etcd_port}")
        print(f"Heartbeat interval from env: {config.heartbeat_interval}")
        print(f"Heartbeat timeout from env: {config.heartbeat_timeout}")
        print(f"Log level from env: {config.log_level}")

    finally:
        # Clean up environment variables
        for key in ['VSWITCH_AGENT_ETCD_HOST', 'VSWITCH_AGENT_ETCD_PORT',
                   'VSWITCH_AGENT_HEARTBEAT_INTERVAL', 'VSWITCH_AGENT_HEARTBEAT_TIMEOUT',
                   'VSWITCH_AGENT_LOG_LEVEL']:
            os.environ.pop(key, None)
    
    print()


def example_5_agent_usage():
    """Example 5: Basic agent usage."""
    print("=== Example 5: Agent Usage ===")
    
    try:
        # Create agent with default configuration
        agent = VSwitchAgent()
        
        print(f"Agent ID: {agent.config.agent_id}")
        print(f"Node ID: {agent.config.node_id}")
        
        # Register custom event handler
        def custom_tenant_handler(event_data):
            print(f"Custom handler: Tenant event received: {event_data}")
        
        agent.event_handler.register('tenant_create', custom_tenant_handler)
        
        # Get agent status
        status = agent.get_status()
        print("Agent status:")
        for key, value in status.items():
            print(f"  {key}: {value}")
        
        # Note: We don't actually start the agent here since it would
        # try to connect to ETCD and run indefinitely
        print("Agent created successfully (not started)")
        
    except Exception as e:
        print(f"Error creating agent: {e}")
    
    print()


def example_6_config_validation():
    """Example 6: Configuration validation."""
    print("=== Example 6: Configuration Validation ===")
    
    manager = ConfigManager()
    
    # Test valid configuration
    try:
        config = manager.load_config()
        print("✓ Default configuration is valid")
    except Exception as e:
        print(f"✗ Default configuration error: {e}")
    
    # Test invalid configuration
    try:
        manager.config.etcd_port = 99999  # Invalid port
        manager._validate_config()
        print("✗ Validation should have failed")
    except ValueError as e:
        print(f"✓ Validation correctly caught error: {e}")
    
    # Reset to valid configuration
    manager.config.etcd_port = 2379
    
    try:
        manager.config.heartbeat_timeout = 10.0
        manager.config.heartbeat_interval = 30.0  # Timeout < interval
        manager._validate_config()
        print("✗ Validation should have failed")
    except ValueError as e:
        print(f"✓ Validation correctly caught error: {e}")
    
    print()


def main():
    """Run all examples."""
    print("V-Switch Agent Configuration Examples")
    print("=" * 50)
    print()
    
    try:
        example_1_basic_config()
        example_2_config_manager()
        example_3_load_from_file()
        example_4_environment_variables()
        example_5_agent_usage()
        example_6_config_validation()
        
        print("All examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()

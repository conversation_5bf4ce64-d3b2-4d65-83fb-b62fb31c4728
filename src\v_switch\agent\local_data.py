import json
import os
from typing import Any


class LocalDataManager:
    """本地数据管理器。"""

    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        self.json_indent = 2
   

    def get_local_shards(self) -> list:
        """获取本地缓存的分片列表。"""
        if not os.path.isdir(self.data_dir):
            return []
        shard_dirs = os.listdir(self.data_dir)
        shards = []
        for shard_dir in shard_dirs:
            shard_path = self.get_local_shard_path(shard_dir)
            if os.path.isdir(shard_path) and shard_dir.isdigit():
                shards.append(shard_dir)
        return shards

    def get_local_shard_path(self, shard_id: str) -> str:
        """获取本地配置文件目录。"""
        return os.path.join(self.data_dir, shard_id)


    def get_local_configs(self, shard_id: str) -> list:
        """获取本地配置文件列表。"""
        shard_path = self.get_local_shard_path(shard_id)
        return [f for f in os.listdir(shard_path) if f.endswith(".json")]


    def get_local_vlans(self, shard_id: str) -> set[str]:
        """
        获取本地缓存的 VLAN ID 列表。
        """
        shard_path = self.get_local_shard_path(shard_id)
        configs = set()
        for f in os.listdir(shard_path):
            if not f.startswith("vlan-") or not f.endswith(".json"):
                continue
            configs.add(f.split("-")[1].split(".")[0])
        return configs


    def get_local_config_path(self, shard_id: str, vlan_id: str) -> str:
        """根据 VLAN ID 获取本地配置文件路径。"""
        return os.path.join(self.data_dir, shard_id, f"vlan-{vlan_id}.json")


    def read_local_config(self, shard_id: str, vlan_id: str) -> dict:
        """读取本地配置文件。"""
        config_path = self.get_local_config_path(shard_id, vlan_id)
        if os.path.exists(config_path):
            with open(config_path, "r") as f:
                return json.load(f)
        return None

    def update_local_config(self, config: dict) -> None:
        """更新本地配置文件。"""
        shard_id = config["shard_id"]
        vlan_id = config["vlan_id"]
        shard_path = self.get_local_shard_path(shard_id)
        os.makedirs(shard_path, exist_ok=True)
        config_path = self.get_local_config_path(shard_id, vlan_id)
        with open(config_path, "w") as f:
            json.dump(config, f, indent=self.json_indent)
        pass


    def delete_local_config(self, config: dict) -> None:
        """删除本地配置文件。"""
        shard_id = config["shard_id"]
        vlan_id = config["vlan_id"]
        config_path = self.get_local_config_path(shard_id, vlan_id)
        if os.path.exists(config_path):
            os.remove(config_path)



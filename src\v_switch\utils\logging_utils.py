"""
Logging utilities for v-switch.
"""

import logging
import logging.handlers
import os
from typing import Optional
from ..config.base_config import LoggingConfig


def setup_logging(config: LoggingConfig, component_name: str = "v-switch") -> None:
    """Setup logging configuration.
    
    Args:
        config: Logging configuration
        component_name: Name of the component for log formatting
    """
    # Create logs directory if it doesn't exist
    if config.file:
        log_dir = os.path.dirname(config.file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        fmt=f'%(asctime)s - {component_name} - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if config.console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, config.level.upper()))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler
    if config.file:
        try:
            # Use rotating file handler to prevent log files from growing too large
            file_handler = logging.handlers.RotatingFileHandler(
                config.file,
                maxBytes=config.max_size,
                backupCount=config.backup_count,
                encoding='utf-8'
            )
            file_handler.setLevel(getattr(logging, config.level.upper()))
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
        except Exception as e:
            # If file logging fails, at least log to console
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.WARNING)
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)
            
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to setup file logging: {e}")
    
    # Log the logging setup
    logger = logging.getLogger(__name__)
    logger.info(f"Logging setup complete for {component_name}")
    logger.info(f"Log level: {config.level}")
    logger.info(f"Console logging: {config.console}")
    logger.info(f"File logging: {config.file if config.file else 'disabled'}")


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def set_log_level(level: str) -> None:
    """Set the log level for all loggers.
    
    Args:
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    try:
        log_level = getattr(logging, level.upper())
        logging.getLogger().setLevel(log_level)
        
        # Update all handlers
        for handler in logging.getLogger().handlers:
            handler.setLevel(log_level)
            
        logger = logging.getLogger(__name__)
        logger.info(f"Log level changed to {level.upper()}")
        
    except AttributeError:
        logger = logging.getLogger(__name__)
        logger.error(f"Invalid log level: {level}")


def log_exception(logger: logging.Logger, message: str, exc_info: bool = True) -> None:
    """Log an exception with context.
    
    Args:
        logger: Logger instance
        message: Context message
        exc_info: Whether to include exception info
    """
    logger.error(message, exc_info=exc_info)


def create_component_logger(component_name: str, config: LoggingConfig) -> logging.Logger:
    """Create a logger for a specific component.
    
    Args:
        component_name: Name of the component
        config: Logging configuration
        
    Returns:
        Logger instance
    """
    # Setup logging if not already done
    if not logging.getLogger().handlers:
        setup_logging(config, component_name)
    
    return logging.getLogger(component_name)


class LogContext:
    """Context manager for temporary log level changes."""
    
    def __init__(self, logger: logging.Logger, level: str):
        """Initialize log context.
        
        Args:
            logger: Logger instance
            level: Temporary log level
        """
        self.logger = logger
        self.new_level = getattr(logging, level.upper())
        self.old_level = None
    
    def __enter__(self):
        """Enter context - set new log level."""
        self.old_level = self.logger.level
        self.logger.setLevel(self.new_level)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context - restore old log level."""
        if self.old_level is not None:
            self.logger.setLevel(self.old_level)

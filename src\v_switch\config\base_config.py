"""
Base configuration class for v-switch components.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field


@dataclass
class ETCDConfig:
    """ETCD connection configuration."""
    host: str = "localhost"
    port: int = 2379
    timeout: float = 5.0
    username: str = ""
    password: str = ""


@dataclass
class LoggingConfig:
    """Logging configuration."""
    level: str = "INFO"
    file: str = ""
    console: bool = True


class BaseConfig:
    """Base configuration class with YAML loading support and environment variable override."""

    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration.

        Args:
            config_file: Path to YAML configuration file
        """
        self.etcd = ETCDConfig()
        self.logging = LoggingConfig()

        if config_file:
            self.load_from_file(config_file)

        # Apply environment variable overrides
        self._apply_env_overrides()

    @classmethod
    def from_file(cls, config_file: str):
        """Create configuration instance from YAML file.

        Args:
            config_file: Path to YAML configuration file

        Returns:
            Configuration instance

        Raises:
            FileNotFoundError: If config file doesn't exist
            yaml.YAMLError: If config file is invalid YAML
        """
        instance = cls()
        instance.load_from_file(config_file)
        instance._apply_env_overrides()
        return instance

    def load_from_file(self, config_file: str) -> None:
        """Load configuration from YAML file.
        
        Args:
            config_file: Path to YAML configuration file
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            yaml.YAMLError: If config file is invalid YAML
        """
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"Configuration file not found: {config_file}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if config_data:
                self._update_from_dict(config_data)
                
        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Invalid YAML in config file {config_file}: {e}")
    
    def _update_from_dict(self, config_data: Dict[str, Any]) -> None:
        """Update configuration from dictionary.
        
        Args:
            config_data: Configuration dictionary
        """
        # Update ETCD config
        if 'etcd' in config_data:
            etcd_config = config_data['etcd']
            for key, value in etcd_config.items():
                if hasattr(self.etcd, key):
                    setattr(self.etcd, key, value)
        
        # Update logging config
        if 'logging' in config_data:
            logging_config = config_data['logging']
            for key, value in logging_config.items():
                if hasattr(self.logging, key):
                    setattr(self.logging, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary.
        
        Returns:
            Configuration as dictionary
        """
        return {
            'etcd': {
                'host': self.etcd.host,
                'port': self.etcd.port,
                'timeout': self.etcd.timeout,
                'username': self.etcd.username,
                'password': self.etcd.password,
            },
            'logging': {
                'level': self.logging.level,
                'file': self.logging.file,
            }
        }
    
    def setup_logging(self) -> None:
        """Setup logging based on configuration."""
        log_level = getattr(logging, self.logging.level.upper(), logging.INFO)

        # Configure logging format
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

        if self.logging.file:
            # Log to file
            logging.basicConfig(
                level=log_level,
                format=log_format,
                filename=self.logging.file,
                filemode='a'
            )
        else:
            # Log to console
            logging.basicConfig(
                level=log_level,
                format=log_format
            )

    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides to configuration.

        Environment variables should follow the pattern:
        VSWITCH_<SECTION>_<KEY> = value

        Examples:
        - VSWITCH_ETCD_HOST=localhost
        - VSWITCH_ETCD_PORT=2379
        - VSWITCH_LOGGING_LEVEL=DEBUG
        """
        env_mappings = self._get_env_mappings()

        for env_var, (section, key, type_converter) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    # Convert the environment variable value to the appropriate type
                    converted_value = type_converter(env_value)

                    # Get the configuration section object
                    config_section = getattr(self, section, None)
                    if config_section and hasattr(config_section, key):
                        setattr(config_section, key, converted_value)
                        print(f"Environment override: {env_var}={converted_value}")

                except (ValueError, TypeError) as e:
                    print(f"Warning: Invalid value for {env_var}={env_value}: {e}")

    def _get_env_mappings(self) -> Dict[str, tuple]:
        """Get mapping of environment variables to configuration paths.

        Returns:
            Dictionary mapping env var names to (section, key, type_converter) tuples
        """
        return {
            # ETCD configuration
            'VSWITCH_ETCD_HOST': ('etcd', 'host', str),
            'VSWITCH_ETCD_PORT': ('etcd', 'port', int),
            'VSWITCH_ETCD_TIMEOUT': ('etcd', 'timeout', float),
            'VSWITCH_ETCD_USERNAME': ('etcd', 'username', str),
            'VSWITCH_ETCD_PASSWORD': ('etcd', 'password', str),

            # Logging configuration
            'VSWITCH_LOGGING_LEVEL': ('logging', 'level', str),
            'VSWITCH_LOGGING_FILE': ('logging', 'file', str),
            'VSWITCH_LOGGING_CONSOLE': ('logging', 'console', self._str_to_bool),
        }

    @staticmethod
    def _str_to_bool(value: str) -> bool:
        """Convert string to boolean.

        Args:
            value: String value to convert

        Returns:
            Boolean value

        Raises:
            ValueError: If value cannot be converted to boolean
        """
        if isinstance(value, bool):
            return value

        value_lower = value.lower().strip()
        if value_lower in ('true', '1', 'yes', 'on', 'enabled'):
            return True
        elif value_lower in ('false', '0', 'no', 'off', 'disabled'):
            return False
        else:
            raise ValueError(f"Cannot convert '{value}' to boolean")

    def get_env_var_help(self) -> str:
        """Get help text for available environment variables.

        Returns:
            Help text describing available environment variables
        """
        help_text = "Available environment variables for configuration override:\n\n"

        env_mappings = self._get_env_mappings()
        sections = {}

        # Group by section
        for env_var, (section, key, type_converter) in env_mappings.items():
            if section not in sections:
                sections[section] = []

            type_name = type_converter.__name__ if hasattr(type_converter, '__name__') else str(type_converter)
            sections[section].append(f"  {env_var:<30} -> {section}.{key} ({type_name})")

        # Format output
        for section, vars_list in sections.items():
            help_text += f"{section.upper()} Configuration:\n"
            help_text += "\n".join(vars_list) + "\n\n"

        help_text += "Examples:\n"
        help_text += "  export VSWITCH_ETCD_HOST=*************\n"
        help_text += "  export VSWITCH_ETCD_PORT=2379\n"
        help_text += "  export VSWITCH_LOGGING_LEVEL=DEBUG\n"

        return help_text

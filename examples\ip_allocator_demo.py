#!/usr/bin/env python3
"""
IP分配器使用示例

演示如何使用IP分配器进行IP地址的分配和释放操作。
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.v_switch.common.etcd_client import ETCDClient
from src.v_switch.core_service.ip_allocator import (
    IPAllocator, 
    IPAllocatorError, 
    IPRangeExhaustedError, 
    InvalidIPError
)


def setup_logging():
    """设置日志配置。"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def demo_ip_allocator():
    """演示IP分配器的基本功能。"""
    print("=== IP分配器演示 ===\n")
    
    # 1. 初始化ETCD客户端
    print("1. 连接到ETCD服务器...")
    try:
        etcd_client = ETCDClient(host="localhost", port=2379, timeout=5.0)
        etcd_client.connect()
        print("✓ ETCD连接成功")
    except Exception as e:
        print(f"✗ ETCD连接失败: {e}")
        print("请确保ETCD服务器正在运行在 localhost:2379")
        return
    
    # 2. 创建IP分配器
    print("\n2. 创建IP分配器...")
    try:
        ip_min = "**************"
        ip_max = "**************"
        key = "/demo/ip_allocator"
        
        allocator = IPAllocator(
            ip_min=ip_min,
            ip_max=ip_max,
            etcd_client=etcd_client,
            key=key
        )
        print(f"✓ IP分配器创建成功，IP范围: {ip_min} - {ip_max}")
    except Exception as e:
        print(f"✗ IP分配器创建失败: {e}")
        return
    
    # 3. 显示初始状态
    print("\n3. 初始分配状态:")
    status = allocator.get_allocation_status()
    print_allocation_status(status)
    
    # 4. 分配一些IP地址
    print("\n4. 分配IP地址...")
    allocated_ips = []
    
    for i in range(5):
        try:
            ip = allocator.allocate_ip()
            allocated_ips.append(ip)
            print(f"✓ 分配IP: {ip}")
        except IPRangeExhaustedError:
            print("✗ IP地址范围已耗尽")
            break
        except Exception as e:
            print(f"✗ 分配IP失败: {e}")
            break
    
    # 5. 显示分配后状态
    print("\n5. 分配后状态:")
    status = allocator.get_allocation_status()
    print_allocation_status(status)
    
    # 6. 释放一些IP地址
    print("\n6. 释放IP地址...")
    if len(allocated_ips) >= 2:
        for ip in allocated_ips[:2]:
            if allocator.release_ip(ip):
                print(f"✓ 释放IP: {ip}")
            else:
                print(f"✗ 释放IP失败: {ip}")
    
    # 7. 显示释放后状态
    print("\n7. 释放后状态:")
    status = allocator.get_allocation_status()
    print_allocation_status(status)
    
    # 8. 再次分配IP（应该优先使用回收的IP）
    print("\n8. 再次分配IP（测试回收机制）...")
    for i in range(3):
        try:
            ip = allocator.allocate_ip()
            print(f"✓ 分配IP: {ip}")
        except Exception as e:
            print(f"✗ 分配IP失败: {e}")
            break
    
    # 9. 显示最终状态
    print("\n9. 最终状态:")
    status = allocator.get_allocation_status()
    print_allocation_status(status)
    
    # 10. 显示详细信息
    print("\n10. 详细信息:")
    print(f"已分配IP列表: {allocator.get_allocated_ips()}")
    print(f"已回收IP列表: {allocator.get_reclaimed_ips()}")
    
    print("\n=== 演示完成 ===")


def print_allocation_status(status):
    """打印分配状态信息。"""
    if not status:
        print("无法获取状态信息")
        return
        
    print(f"  IP范围: {status['ip_range']}")
    print(f"  总IP数: {status['total_ips']}")
    print(f"  已分配: {status['allocated_count']}")
    print(f"  已回收: {status['reclaimed_count']}")
    print(f"  可用数: {status['available_count']}")
    print(f"  下一个IP: {status['next_ip']}")
    print(f"  使用率: {status['utilization_rate']:.2f}%")


def demo_error_handling():
    """演示错误处理。"""
    print("\n=== 错误处理演示 ===\n")
    
    # 模拟ETCD客户端（不实际连接）
    from unittest.mock import Mock
    mock_etcd = Mock()
    mock_etcd.get_json.return_value = (None, None)
    mock_etcd.put_json.return_value = True
    
    # 1. 无效IP地址范围
    print("1. 测试无效IP地址...")
    try:
        IPAllocator("invalid_ip", "**************", mock_etcd, "/test")
    except InvalidIPError as e:
        print(f"✓ 捕获到预期错误: {e}")
    
    # 2. 无效IP范围（最小IP >= 最大IP）
    print("\n2. 测试无效IP范围...")
    try:
        IPAllocator("**************", "**************", mock_etcd, "/test")
    except InvalidIPError as e:
        print(f"✓ 捕获到预期错误: {e}")
    
    # 3. IP范围耗尽
    print("\n3. 测试IP范围耗尽...")
    try:
        # 创建只有1个IP的分配器
        allocator = IPAllocator("**************", "**************", mock_etcd, "/test")
        
        # 分配第一个IP
        ip1 = allocator.allocate_ip()
        print(f"✓ 分配第一个IP: {ip1}")
        
        # 尝试分配第二个IP（应该失败）
        allocator.allocate_ip()
        
    except IPRangeExhaustedError as e:
        print(f"✓ 捕获到预期错误: {e}")
    
    print("\n=== 错误处理演示完成 ===")


def main():
    """主函数。"""
    setup_logging()
    
    print("IP分配器演示程序")
    print("================")
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--error-demo":
        demo_error_handling()
    else:
        print("\n注意: 此演示需要ETCD服务器运行在 localhost:2379")
        print("如果只想看错误处理演示，请使用: python ip_allocator_demo.py --error-demo\n")
        
        try:
            demo_ip_allocator()
        except KeyboardInterrupt:
            print("\n\n用户中断，程序退出")
        except Exception as e:
            print(f"\n演示过程中发生错误: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()

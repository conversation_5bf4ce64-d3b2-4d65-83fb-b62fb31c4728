# FastAPI Migration for V-Switch API Server

## 概述

v-switch API 服务器已从标准库的 `http.server` 迁移到 FastAPI 框架，提供了更好的性能、自动文档生成和现代化的 API 开发体验。

## 主要改进

### 1. 现代化框架
- **FastAPI**: 基于 Starlette 和 Pydantic 的高性能异步 Web 框架
- **自动文档**: 自动生成 OpenAPI/Swagger 文档
- **类型安全**: 使用 Pydantic 模型进行请求/响应验证
- **异步支持**: 原生支持异步操作

### 2. 性能提升
- **Uvicorn**: 高性能 ASGI 服务器
- **异步处理**: 支持并发请求处理
- **更好的资源利用**: 减少内存和 CPU 使用

### 3. 开发体验
- **自动验证**: 请求数据自动验证和类型转换
- **错误处理**: 统一的错误响应格式
- **API 文档**: 访问 `/docs` 查看交互式 API 文档

## API 端点

### 健康检查
```
GET /health
```
返回服务健康状态。

### 服务器状态
```
GET /status
```
返回服务器运行状态和元数据。

### 创建子网网关
```
POST /network/subnet-gateway
Content-Type: application/json

{
  "tenant_id": "string",
  "vlan_id": 100,
  "subnet_gw_ip": "***********"
}
```

### 创建 EIP
```
POST /network/eip
Content-Type: application/json

{
  "tenant_id": "string",
  "vlan_id": 100,
  "eip": "***********",
  "gateway_ip": "***********",
  "internal_ip": "************"
}
```

### 获取指令状态
```
GET /instruction/{request_id}?tenant_id=string
```

### 删除指令
```
DELETE /instruction/{request_id}?tenant_id=string
```

## 数据模型

### 请求模型

#### SubnetGatewayRequest
```python
class SubnetGatewayRequest(BaseModel):
    tenant_id: str
    vlan_id: int
    subnet_gw_ip: str
```

#### EIPRequest
```python
class EIPRequest(BaseModel):
    tenant_id: str
    vlan_id: int
    eip: str
    gateway_ip: str
    internal_ip: str
```

### 响应模型

#### SuccessResponse
```python
class SuccessResponse(BaseModel):
    success: bool = True
    request_id: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
```

#### ErrorResponse
```python
class ErrorResponse(BaseModel):
    error: bool = True
    message: str
    status_code: int
```

## 配置

服务器配置保持不变，仍使用 `config/server_config.yaml`：

```yaml
server:
  port: 30090
  shard_count: 32

etcd:
  host: *************
  port: 30379
  timeout: 5.0
  username: ''
  password: ''

logging:
  level: INFO
  file: ''
```

## 启动服务

### 开发模式
```bash
python scripts/start_server.py --config config/server_config.yaml
```

### 测试
```bash
python test_fastapi_server.py
```

## API 文档

启动服务后，可以通过以下 URL 访问自动生成的 API 文档：

- **Swagger UI**: http://localhost:30090/docs
- **ReDoc**: http://localhost:30090/redoc
- **OpenAPI JSON**: http://localhost:30090/openapi.json

## 兼容性

- **向后兼容**: API 端点和响应格式保持不变
- **配置兼容**: 使用相同的配置文件格式
- **部署兼容**: 可以直接替换原有实现

## 依赖

新增的依赖项：
- `fastapi>=0.104.0`
- `uvicorn[standard]>=0.24.0`
- `pydantic>=2.0.0`

这些依赖已添加到 `pyproject.toml` 中。

## 迁移说明

1. **无需更改客户端代码**: API 接口保持完全兼容
2. **配置文件不变**: 继续使用现有配置
3. **部署脚本**: 可能需要更新依赖安装步骤
4. **监控**: 可以利用 FastAPI 的内置监控功能

## 故障排除

### 常见问题

1. **端口占用**: 确保配置的端口未被其他服务占用
2. **依赖缺失**: 运行 `pip install fastapi uvicorn[standard] pydantic`
3. **配置错误**: 检查 `config/server_config.yaml` 格式

### 日志

FastAPI 服务器会输出详细的访问日志和错误信息，便于调试和监控。

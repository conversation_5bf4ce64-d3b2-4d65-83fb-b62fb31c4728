[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "v-switch"
version = "0.1.0"
description = "A Python project for v-switch network management"
readme = "README.md"
requires-python = ">=3.8"
license = {text = "MIT"}
authors = [
    {name = "V-Switch Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    # Core web framework dependencies
    "fastapi==0.104.0",
    "uvicorn[standard]>=0.24.0",
    "pydantic==2.11.7",

    # Network and system dependencies
    "protobuf==3.20.3",
    "uhashring==2.4",
    "pyyaml==6.0.2",
    "etcd3==0.12.0",
    "grpcio==1.73.1",
    "six==1.17.0",
    "tenacity==9.1.2",
    "click==8.2.1",
    "h11==0.16.0",
    "typing-extensions==4.14.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "black>=23.0",
    "flake8>=6.0",
    "mypy>=1.0",
]

[project.scripts]
v-switch-agent = "v_switch.scripts.start_agent:main"
v-switch-server = "v_switch.scripts.start_server:main"

[project.urls]
Homepage = "http://*************/nj-projects/cloud-link/v-switch"
Repository = "http://*************/nj-projects/cloud-link/v-switch.git"
Issues = "http://*************/nj-projects/cloud-link/v-switch/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=v_switch --cov-report=html --cov-report=term-missing"

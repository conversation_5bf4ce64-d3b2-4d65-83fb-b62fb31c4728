#!/bin/bash
# Example script showing how to use environment variables to override configuration

echo "V-Switch Environment Variable Configuration Examples"
echo "===================================================="
echo

# Example 1: Development environment
echo "Example 1: Development Environment"
echo "-----------------------------------"
echo "Setting up development environment with local ETCD and debug logging:"
echo
echo "export VSWITCH_ETCD_HOST=localhost"
echo "export VSWITCH_ETCD_PORT=2379"
echo "export VSWITCH_LOGGING_LEVEL=DEBUG"
echo "export VSWITCH_SERVER_PORT=8080"
echo
export VSWITCH_ETCD_HOST=localhost
export VSWITCH_ETCD_PORT=2379
export VSWITCH_LOGGING_LEVEL=DEBUG
export VSWITCH_SERVER_PORT=8080

echo "Starting server with development settings..."
echo "python -m v_switch.server --config config/server_config.yaml"
echo

# Example 2: Production environment
echo "Example 2: Production Environment"
echo "----------------------------------"
echo "Setting up production environment with remote ETCD cluster:"
echo
echo "export VSWITCH_ETCD_HOST=etcd-cluster.production.local"
echo "export VSWITCH_ETCD_PORT=2379"
echo "export VSWITCH_ETCD_USERNAME=vswitch_user"
echo "export VSWITCH_ETCD_PASSWORD=secure_password"
echo "export VSWITCH_LOGGING_LEVEL=INFO"
echo "export VSWITCH_LOGGING_FILE=/var/log/vswitch/server.log"
echo "export VSWITCH_SERVER_PORT=30090"
echo "export VSWITCH_SERVER_SHARD_COUNT=128"
echo

# Example 3: Agent configuration
echo "Example 3: Agent Configuration"
echo "-------------------------------"
echo "Setting up agent with custom settings:"
echo
echo "export VSWITCH_AGENT_ID=agent-\$(hostname)"
echo "export VSWITCH_AGENT_HEARTBEAT_INTERVAL=60.0"
echo "export VSWITCH_AGENT_MAX_RETRIES=5"
echo "export VSWITCH_ETCD_HOST=etcd-cluster.production.local"
echo "export VSWITCH_LOGGING_LEVEL=INFO"
echo

# Example 4: Docker environment
echo "Example 4: Docker Environment"
echo "------------------------------"
echo "Docker run command with environment variables:"
echo
echo "docker run -d \\"
echo "  -e VSWITCH_ETCD_HOST=etcd-service \\"
echo "  -e VSWITCH_ETCD_PORT=2379 \\"
echo "  -e VSWITCH_SERVER_PORT=8080 \\"
echo "  -e VSWITCH_LOGGING_LEVEL=INFO \\"
echo "  -p 8080:8080 \\"
echo "  vswitch:latest"
echo

# Example 5: Kubernetes environment
echo "Example 5: Kubernetes Environment"
echo "----------------------------------"
echo "Kubernetes ConfigMap and environment variables:"
echo
cat << 'EOF'
apiVersion: v1
kind: ConfigMap
metadata:
  name: vswitch-config
data:
  VSWITCH_ETCD_HOST: "etcd-service.default.svc.cluster.local"
  VSWITCH_ETCD_PORT: "2379"
  VSWITCH_LOGGING_LEVEL: "INFO"
  VSWITCH_SERVER_PORT: "8080"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vswitch-server
spec:
  replicas: 3
  selector:
    matchLabels:
      app: vswitch-server
  template:
    metadata:
      labels:
        app: vswitch-server
    spec:
      containers:
      - name: vswitch
        image: vswitch:latest
        envFrom:
        - configMapRef:
            name: vswitch-config
        env:
        - name: VSWITCH_AGENT_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        ports:
        - containerPort: 8080
EOF
echo

# Example 6: Testing different configurations
echo "Example 6: Testing Different Configurations"
echo "--------------------------------------------"
echo "Quick test with different settings:"
echo
echo "# Test with high verbosity"
echo "VSWITCH_LOGGING_LEVEL=DEBUG python scripts/test_env_override.py"
echo
echo "# Test with different ETCD settings"
echo "VSWITCH_ETCD_HOST=test-etcd VSWITCH_ETCD_PORT=12379 python scripts/show_env_vars.py"
echo

echo "Environment Variable Priority:"
echo "------------------------------"
echo "1. Environment variables (highest priority)"
echo "2. Configuration file values"
echo "3. Default values (lowest priority)"
echo

echo "Tips:"
echo "-----"
echo "- Use 'python scripts/show_env_vars.py' to see all available environment variables"
echo "- Use 'python scripts/test_env_override.py' to test environment variable functionality"
echo "- Environment variables are applied after loading the configuration file"
echo "- Invalid environment variable values are ignored with a warning message"
echo "- Boolean values accept: true/false, 1/0, yes/no, on/off, enabled/disabled"
echo

# Clean up example environment variables
unset VSWITCH_ETCD_HOST
unset VSWITCH_ETCD_PORT
unset VSWITCH_LOGGING_LEVEL
unset VSWITCH_SERVER_PORT

echo "Done! You can now use environment variables to override any configuration setting."

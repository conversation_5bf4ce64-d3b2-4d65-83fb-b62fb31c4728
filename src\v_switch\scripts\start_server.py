#!/usr/bin/env python3
"""
Start v-switch server.
"""

import sys
import signal
import argparse
import subprocess
import os
from pathlib import Path

from v_switch.config.server_config import ServerConfig
from v_switch.core_service.core_service import CoreService
from v_switch.api_server.api_server import APIServer
from v_switch.utils.logging_utils import setup_logging


def signal_handler(signum, frame):
    """Handle shutdown signals."""
    print(f"\nReceived signal {signum}, shutting down...")
    sys.exit(0)


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Start v-switch server")
    parser.add_argument(
        "--config",
        default="config/server_config.yaml",
        help="Path to server configuration file"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Run in dry-run mode (log commands but don't execute)"
    )

    args = parser.parse_args()

    try:
        # Load configuration
        print(f"Loading configuration from {args.config}")
        config = ServerConfig.from_file(args.config)

        # Setup logging
        setup_logging(config.logging, "v-switch-server")

        # Create core service
        print("Initializing core service...")
        core_service = CoreService(config)

        # Create API server
        print("Initializing API server...")
        api_server = APIServer(config, core_service)

        # Setup signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Start services
        print("Starting core service...")
        if not core_service.start():
            print("Failed to start core service")
            return 1

        print("Starting API server...")
        if not api_server.start():
            print("Failed to start API server")
            core_service.stop()
            return 1

        print(f"V-Switch server started successfully on port {config.server.port}")
        print("Press Ctrl+C to stop")

        # Keep running
        try:
            while True:
                signal.pause()
        except KeyboardInterrupt:
            pass

    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        # Cleanup
        print("Stopping services...")
        try:
            if 'api_server' in locals():
                api_server.stop()
            if 'core_service' in locals():
                core_service.stop()
        except Exception as e:
            print(f"Error during cleanup: {e}")

        print("Server stopped")

    return 0


if __name__ == "__main__":
    sys.exit(main())

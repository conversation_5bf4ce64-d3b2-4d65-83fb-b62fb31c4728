"""
Main agent class for v-switch.
"""

import json
import logging
import threading
import os
import uuid
from typing import Dict, Any, List, Optional, Set
from etcd3.events import PutEvent, DeleteEvent
from v_switch.config.agent_config import AgentConfig
from v_switch.common.etcd_client import ETCDClient
from v_switch.common.models import SubnetConfigParams, TaskData, TaskStatus, TaskType, ServerMetadata
from v_switch.agent.heartbeat_manager import HeartbeatManager
from v_switch.agent.local_data import LocalDataManager
from v_switch.agent.environment_checker import <PERSON><PERSON>he<PERSON>
from v_switch.agent.command_processor import CommandProcessor


class VSwitchAgent:
    """Main v-switch agent class."""

    METADATA_KEY = "/network/server/metadata"
    SUBNET_PREFIX = "/network/server/subnet/"
    TASK_PREFIX = "/network/agent/task/"
    # Data directory for local configs
    DATA_DIR = "./data/subnet"

    def __init__(self, config: AgentConfig, dry_run: bool = False):
        """初始化 agent.

        Args:
            config: Agent configuration
            dry_run: 如果为 True，则命令会被记录但不会执行
        """
        self.config = config
        self.dry_run = dry_run
        self.logger = logging.getLogger(__name__)

        os.makedirs(self.DATA_DIR, exist_ok=True)

        # Initialize ETCD client
        self.etcd_client = ETCDClient(
            host=config.etcd.host,
            port=config.etcd.port,
            timeout=config.etcd.timeout,
            username=config.etcd.username,
            password=config.etcd.password
        )

        # Initialize components
        self.local_data = LocalDataManager(self.DATA_DIR)
        self.env_checker = EnvironmentChecker(config.env_check)
        self.heartbeat_manager = HeartbeatManager(self.etcd_client, config, self.env_checker)
        self.command_processor = CommandProcessor(local_data=self.local_data, dry_run=dry_run)

        # State management
        self._running = False
        self._metadata_watch_thread = None
        self._subnet_watch_threads: Dict[str, threading.Thread] = {}
        self._metadata_initialized = False

    def start(self) -> bool:
        """启动 agent

        Returns:
            True if successful
        """
        try:
            self.logger.info("Starting v-switch agent...")

            # 连接到 ETCD
            self.etcd_client.connect()

            # 首先启动元数据监控并等待完成初始化
            self.logger.info("Starting metadata monitoring...")
            self._start_metadata_monitoring()

            # 等待元数据监控完成
            if not self._wait_for_metadata_monitoring():
                self.logger.error("Failed to start metadata monitoring")
                return False
            self.logger.info("Metadata monitoring initialized successfully")

            # 元数据监听完成后，启动心跳管理器
            if not self.heartbeat_manager.start():
                self.logger.error("Failed to start heartbeat manager")
                return False

            self._running = True
            self.logger.info("V-switch agent started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start agent: {e}")
            return False

    def stop(self) -> None:
        """停止 agent."""
        try:
            self.logger.info("Stopping v-switch agent...")
            self._running = False

            # 停止子网监控
            self._stop_subnet_watching()

            # 停止元数据监控
            if self._metadata_watch_thread and self._metadata_watch_thread.is_alive():
                self._metadata_watch_thread.join(timeout=5.0)

            # 停止心跳管理器
            self.heartbeat_manager.stop()

            self.logger.info("V-switch agent stopped")

        except Exception as e:
            self.logger.error(f"Error stopping agent: {e}")

    def _wait_for_metadata_monitoring(self) -> bool:
        try:
            # 等待元数据监听完成初始化
            import time
            max_wait = 10  # 最多等待10秒
            wait_count = 0
            while not self._metadata_initialized and wait_count < max_wait:
                time.sleep(1)
                wait_count += 1

            if not self._metadata_initialized:
                self.logger.error("Metadata monitoring initialization timeout")
                return False
        except Exception as e:
            self.logger.error(f"Failed to wait for metadata monitoring: {e}")
            return False

    def _start_metadata_monitoring(self) -> None:
        """启动元数据监控。"""
        try:
            # 首先获取当前元数据并初始化分片分配
            self._update_assigned_shards()
            self._metadata_watch_thread = threading.Thread(
                target=self._monitor_metadata,
                name="MetadataMonitor",
                daemon=True
            )
            self._metadata_watch_thread.start()
            self.logger.info("Started metadata monitoring")

        except Exception as e:
            self.logger.error(f"Error starting metadata monitoring: {e}")

    def _monitor_metadata(self) -> None:
        """监控服务器元数据变化。"""
        try:
            # 标记元数据监听已初始化
            self._metadata_initialized = True
            self.logger.info("Metadata monitoring initialized")

            # 监听元数据变化
            for event in self.etcd_client.watch_key(self.METADATA_KEY):
                if not self._running:
                    break
                if isinstance(event, PutEvent):
                    metadata_data = json.loads(event.value.decode())
                    self._update_assigned_shards(metadata_data)

        except Exception as e:
            self.logger.error(f"Error in metadata monitoring: {e}")
            self._metadata_initialized = False

    def _update_assigned_shards(self, metadata_data: Optional[Dict[str, Any]] = None) -> None:
        """更新分配的分片列表。"""
        try:

            if not metadata_data:
                metadata_data, _ = self.etcd_client.get_json(self.METADATA_KEY)
                if not metadata_data:
                    return

            metadata = ServerMetadata.from_dict(metadata_data)

            # 查找当前agent的分片
            new_shards = set()
            for agent in metadata.agent:
                if agent.agent_id == self.config.agent.agent_id:
                    new_shards = set(agent.shard)
                    break

            # 检查分片变化
            assined_shards = self._get_assigned_shards()
            added_shards = new_shards - assined_shards
            removed_shards = assined_shards - new_shards

            if added_shards:
                self.logger.info(f"Added shards: {added_shards}")
                for shard in added_shards:
                    self._syn_local_configs(shard)
                    self._start_shard_monitoring(shard)

            if removed_shards:
                self.logger.info(f"Removed shards: {removed_shards}")
                for shard in removed_shards:
                    self._stop_shard_monitoring(shard)
                    self._delete_local_configs(shard)

        except Exception as e:
            self.logger.error(f"Error updating assigned shards: {e}")

    def _start_shard_monitoring(self, shard_id: str) -> None:
        """启动分片监控。"""
        try:
            if shard_id in self._subnet_watch_threads:
                return

            thread = threading.Thread(
                target=self._monitor_shard_subnets,
                args=(shard_id,),
                name=f"ShardMonitor-{shard_id}",
                daemon=True
            )
            thread.start()
            self._subnet_watch_threads[shard_id] = thread

            self.logger.info(f"Started monitoring shard {shard_id}")

        except Exception as e:
            self.logger.error(f"Error starting shard monitoring for {shard_id}: {e}")

    def _stop_shard_monitoring(self, shard_id: str) -> None:
        """停止分片监控。

        Args:
            shard_id: Shard identifier
            del_local: 是否删除本地配置
        """
        try:
            if shard_id in self._subnet_watch_threads:
                thread = self._subnet_watch_threads.pop(shard_id)
                if thread and thread.is_alive():
                    thread.join(timeout=5.0)
                self.logger.info(f"Stopped monitoring shard {shard_id}")

        except Exception as e:
            self.logger.error(f"Error stopping shard monitoring for {shard_id}: {e}")

    def _syn_local_configs(self, shard_id: str) -> None:
        """同步本地配置文件。
        1. 比对本地配置文件列表和ETCD中的配置数据列表(注意: 本地配置文件多一个 .json 后缀)
        2. 删除多余的本地配置文件
        3. 添加缺失的本地配置文件
        4. 同步时 执行 command_processor

        Args:
            shard_id: Shard identifier
        """
        try:
            # 1. 获取本地缓存的 VLAN ID 列表
            vlan_ids = self.local_data.get_local_vlans(shard_id)

            # 2. 合并本地 vlan 列表和ETCD中的 vlan 列表
            remote_configs = {}
            subnet_prefix = f"{self.SUBNET_PREFIX}{shard_id}/"
            for _, value in self.etcd_client.get_prefix(subnet_prefix):
                vlan_data = json.loads(value)
                vlan_id = vlan_data["vlan_id"]
                vlan_ids.add(vlan_id)
                remote_configs[vlan_id] = SubnetConfigParams.from_dict(vlan_data)
            # 3. 同步配置
            for vlan_id in vlan_ids:
                self.logger.info(f"Syncing local config for shard {shard_id} and vlan {vlan_id}")
                local_value = self.local_data.read_local_config(shard_id, vlan_id)

                local_config = SubnetConfigParams.from_dict(local_value) if local_value else None
                remote_config = remote_configs.get(vlan_id)

                self.command_processor.process_config_changes(task_data=None,
                                                              remote_config=remote_config,
                                                              local_config=local_config)
        except Exception as e:
            self.logger.error(f"Error syn local configs for shard {shard_id}: {e}")
        pass

    def _delete_local_configs(self, shard_id: str) -> None:
        """删除本地配置文件。"""
        try:
            shard_dir = self.local_data.get_local_shard_path(shard_id)
            for file_name in os.listdir(shard_dir):
                self.logger.info(f"Deleting local config for shard {shard_id} and  {shard_dir}")
                local_config_path = os.path.join(shard_dir, file_name)
                local_config = SubnetConfigParams.from_file(local_config_path)
                self.command_processor.process_config_changes(None, None, local_config)
                os.remove(local_config_path)

        except Exception as e:
            self.logger.error(f"Error deleting local configs for shard {shard_id}: {e}")

    def _stop_subnet_watching(self) -> None:
        """停止所有子网监控。"""
        try:
            for shard_id in list(self._subnet_watch_threads.keys()):
                self._stop_shard_monitoring(shard_id)

        except Exception as e:
            self.logger.error(f"Error stopping subnet watching: {e}")

    def _monitor_shard_subnets(self, shard_id: str) -> None:
        """监控分片的子网配置参数。"""
        try:
            subnet_prefix = f"{self.SUBNET_PREFIX}{shard_id}/"

            # 监听子网配置参数变化
            for event in self.etcd_client.watch_prefix(subnet_prefix):
                if not self._running:
                    break
                if isinstance(event, PutEvent):
                    self._handle_subnet_config_change(event, shard_id)

        except Exception as e:
            self.logger.error(f"Error monitoring shard {shard_id} subnets: {e}")

    def _handle_subnet_config_change(self, event: PutEvent, shard_id: str) -> None:
        """处理子网配置参数变化。"""
        try:
            subnet_data = json.loads(event.value.decode())
            subnet_params = SubnetConfigParams.from_dict(subnet_data)

            self.logger.info(f"Subnet config changed for VLAN {subnet_params.vlan_id}, status: {subnet_params.status.value}")

            # 只处理updating和deletion状态的配置
            if subnet_params.status.value in ["updating", "deletion"]:
                self._process_subnet_config(subnet_params, shard_id)

        except Exception as e:
            self.logger.error(f"Error handling subnet config change: {e}")

    def _process_subnet_config(self, subnet_params: SubnetConfigParams, shard_id: str) -> None:
        """处理子网配置参数。"""
        try:
            vlan_id = subnet_params.vlan_id

            # 创建任务数据
            task_id = str(uuid.uuid4())
            task_type = TaskType.UPDATE if subnet_params.status.value == "updating" else TaskType.DELETE

            # 获取本地配置（如果存在）
            local_config_data = self.local_data.read_local_config(shard_id, vlan_id)
            local_config = SubnetConfigParams.from_dict(local_config_data) if local_config_data else None

            # 创建任务数据
            task_data = TaskData(
                task_id=task_id,
                shard_id=shard_id,
                vlan_id=vlan_id,
                type=task_type,
                version=subnet_params.version,
                status=TaskStatus.RUNNING,
                update_config=True,
                cmds=[],
                plan=0,
                message=""
            )

            # 任务数据写入etcd
            task_key = f"{self.TASK_PREFIX}{task_id}"
            success = self.etcd_client.put_json(task_key, task_data.to_dict())

            if success:
                self.logger.info(f"Created task {task_id} for VLAN {vlan_id}")

                # 处理配置变更（比对差异并执行命令）
                remote_config = subnet_params if task_type == TaskType.UPDATE else None
                success = self.command_processor.process_config_changes(
                    task_data=task_data,
                    remote_config=remote_config,
                    local_config=local_config
                )

                # 更新任务状态到etcd
                self.etcd_client.put_json(task_key, task_data.to_dict())

            else:
                self.logger.error(f"Failed to create task for VLAN {subnet_params.vlan_id}")

        except Exception as e:
            self.logger.error(f"Error processing subnet config: {e}")

    def is_running(self) -> bool:
        """检查agent是否正在运行。"""
        return self._running

        # 获取监听中的分片列表

    def _get_assigned_shards(self) -> Set[str]:
        return set(self._subnet_watch_threads.keys())

"""
Heartbeat manager for v-switch agent.
"""

import json
import time
import threading
import logging
from typing import Optional, Dict, Any
from ..common.etcd_client import ETCDClient
from ..config.agent_config import AgentConfig
from .environment_checker import EnvironmentChecker


class HeartbeatManager:
    """管理 agent 心跳，以及注册"""

    def __init__(self, etcd_client: ETCDClient, config: AgentConfig, env_checker: EnvironmentChecker):
        """
        初始化心跳管理器

        Args:
            etcd_client: ETCD client instance
            config: Agent configuration
            env_checker: Environment checker instance
        """
        self.etcd_client = etcd_client
        self.config = config
        self.env_checker = env_checker
        self.logger = logging.getLogger(__name__)

        self._heartbeat_thread = None
        self._running = False
        self._lease_id = None
        self._current_status = "unknown"
        self._register_key = f"/network/agent/register/{config.agent.agent_id}"
    
    def start(self) -> bool:
        """
        启动心跳管理器

        Returns:
            True if successful
        """
        try:
            if self._running:
                self.logger.warning("Heartbeat manager is already running")
                return True

            # 启动心跳线程
            self._running = True
            self._heartbeat_thread = threading.Thread(
                target=self._heartbeat_loop,
                name="HeartbeatManager",
                daemon=True
            )
            self._heartbeat_thread.start()

            self.logger.info("Heartbeat manager started successfully")
            return True

        except Exception as e:
            self.logger.error(f"Failed to start heartbeat manager: {e}")
            return False
    
    def stop(self) -> None:
        """停止心跳管理器"""
        try:
            self.logger.info("Stopping heartbeat manager...")
            self._running = False
            
            # 等待心跳线程完成
            if self._heartbeat_thread and self._heartbeat_thread.is_alive():
                self._heartbeat_thread.join(timeout=5.0)
            
            # Clean up registration
            self._unregister()
            
            self.logger.info("Heartbeat manager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping heartbeat manager: {e}")


    def _heartbeat_loop(self) -> None:
        """主要心跳循环"""
        while self._running:
            try:
                
                # 执行环境检查
                env_ok, env_errors = self.env_checker.check_all()
                if not env_ok:
                    self._current_status = "error"
                    self.logger.error(f"Environment check failed: {env_errors}")
                else:
                    self._current_status = "running"
                    self.logger.debug("Environment check passed")
                
                # 更新心跳时间并提交到etcd
                if not self._update_heartbeat():
                    self.logger.error("Failed to update heartbeat")

            except Exception as e:
                self.logger.error(f"Error in heartbeat loop: {e}")

            # 休眠直到下一次心跳
            time.sleep(self.config.agent.heartbeat_interval)


    def _update_heartbeat(self) -> bool:
        """更新心跳时间到ETCD

        Returns:
            True if successful
        """
        # 获取现有注册数据
        existing_data, metadata = self.etcd_client.get_json(self._register_key)

        if existing_data:
            # 更新现有数据
            existing_data["status"] = self._current_status
            existing_data["lase_heartbeat_time"] = int(time.time() * 1000)
            registration_data = existing_data
        else:
            # 创建新的注册数据
            registration_data = {
                "status": self._current_status,
                "agent_id": self.config.agent.agent_id,
                "lase_heartbeat_time": int(time.time() * 1000)
            }
        # 刷新租约
        if metadata and metadata.lease_id != 0:
            self._lease_id = metadata.lease_id
            self.logger.debug(f"Refreshing lease {self._lease_id}")
            if not self.etcd_client.refresh_lease(self._lease_id):
                self.logger.error("Failed to refresh lease")
                return False
        else:
            lease_ttl = int(self.config.agent.heartbeat_interval * 3)
            self._lease_id = self.etcd_client.create_lease(lease_ttl)
            if not self._lease_id:
                self.logger.error("Failed to create lease")
                return False

        success = self.etcd_client.put_json(self._register_key, registration_data, lease=self._lease_id)

        if success:
            self.logger.debug(f"Updated heartbeat for agent {self.config.agent.agent_id} with status {self._current_status}")
        else:
            self.logger.error(f"Failed to update heartbeat for agent {self.config.agent.agent_id}")

        return success
    
    def _unregister(self) -> bool:
        """从ETCD注销agent

        Returns:
            True if successful
        """
        try:
            success = self.etcd_client.delete(self._register_key)

            if success:
                self.logger.info(f"Unregistered agent {self.config.agent.agent_id}")
            else:
                self.logger.error(f"Failed to unregister agent {self.config.agent.agent_id}")

            # 撤销租约
            if self._lease_id:
                self.etcd_client.revoke_lease(self._lease_id)
                self._lease_id = None

            return success

        except Exception as e:
            self.logger.error(f"Error unregistering agent: {e}")
            return False
  